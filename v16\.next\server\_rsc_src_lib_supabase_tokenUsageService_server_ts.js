"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_tokenUsageService_server_ts";
exports.ids = ["_rsc_src_lib_supabase_tokenUsageService_server_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/tokenUsageService.server.ts":
/*!******************************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.server.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTokenUsageStats: () => (/* binding */ getTokenUsageStats),\n/* harmony export */   saveTokenUsageServer: () => (/* binding */ saveTokenUsageServer),\n/* harmony export */   updateUserPlanLimits: () => (/* binding */ updateUserPlanLimits)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/plans */ \"(rsc)/./src/config/plans.ts\");\n\n\n/**\n * Guarda el uso de tokens en Supabase (versión servidor)\n */ async function saveTokenUsageServer(data) {\n    try {\n        console.log('🔄 saveTokenUsageServer iniciado con data:', data);\n        // Crear cliente de Supabase para el servidor\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        console.log('✅ Cliente Supabase del servidor creado');\n        // Obtener el usuario actual\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');\n        if (userError || !user) {\n            console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);\n            return;\n        }\n        const usageRecord = {\n            user_id: user.id,\n            activity_type: data.activity,\n            model_name: data.model,\n            prompt_tokens: data.usage.promptTokens,\n            completion_tokens: data.usage.completionTokens,\n            total_tokens: data.usage.totalTokens,\n            estimated_cost: data.usage.estimatedCost || 0,\n            usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n        };\n        console.log('📝 Registro a insertar:', usageRecord);\n        const { error } = await supabase.from('user_token_usage').insert([\n            usageRecord\n        ]);\n        if (error) {\n            console.error('❌ Error al guardar uso de tokens:', error);\n            return;\n        }\n        console.log('✅ Registro insertado exitosamente en user_token_usage');\n        // Validar límites antes de actualizar contador\n        const canUpdate = await validateTokenLimits(supabase, user.id, data.usage.totalTokens);\n        if (!canUpdate.allowed) {\n            console.warn('⚠️ Límite de tokens alcanzado:', canUpdate.reason);\n            // Aún así guardamos el registro para auditoría, pero marcamos el exceso\n            return;\n        }\n        // Actualizar contador mensual del usuario\n        await updateMonthlyTokenCount(supabase, user.id, data.usage.totalTokens);\n    } catch (error) {\n        console.error('❌ Error en saveTokenUsageServer:', error);\n    }\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ async function updateMonthlyTokenCount(supabase, userId, tokens) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener o crear perfil del usuario\n        let { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (profileError && profileError.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', profileError);\n            return;\n        }\n        if (!profile) {\n            // Crear perfil nuevo con límites dinámicos\n            const defaultPlan = 'free';\n            const tokenLimit = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(defaultPlan);\n            const { error: insertError } = await supabase.from('user_profiles').insert([\n                {\n                    user_id: userId,\n                    subscription_plan: defaultPlan,\n                    monthly_token_limit: tokenLimit,\n                    current_month_tokens: tokens,\n                    current_month: currentMonth,\n                    payment_verified: false\n                }\n            ]);\n            if (insertError) {\n                console.error('Error al crear perfil:', insertError);\n            } else {\n                console.log('✅ Perfil de usuario creado con límite dinámico:', tokenLimit);\n            }\n        } else {\n            // Actualizar perfil existente\n            const newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n            const { error: updateError } = await supabase.from('user_profiles').update({\n                current_month_tokens: newTokenCount,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', userId);\n            if (updateError) {\n                console.error('Error al actualizar perfil:', updateError);\n            } else {\n                console.log('✅ Perfil de usuario actualizado');\n            }\n        }\n    } catch (error) {\n        console.error('Error en updateMonthlyTokenCount:', error);\n    }\n}\n/**\n * Valida si el usuario puede usar la cantidad de tokens especificada\n */ async function validateTokenLimits(supabase, userId, tokensToUse) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener perfil del usuario\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, monthly_token_limit, current_month_tokens, current_month, payment_verified').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                allowed: false,\n                reason: 'Perfil de usuario no encontrado'\n            };\n        }\n        // Verificar pago para planes de pago\n        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                allowed: false,\n                reason: 'Pago no verificado'\n            };\n        }\n        // Calcular tokens actuales (reset si es nuevo mes)\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        // Verificar límite\n        if (currentTokens + tokensToUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual alcanzado: ${currentTokens + tokensToUse}/${profile.monthly_token_limit}`,\n                currentUsage: {\n                    current: currentTokens,\n                    limit: profile.monthly_token_limit,\n                    requested: tokensToUse,\n                    plan: profile.subscription_plan\n                }\n            };\n        }\n        return {\n            allowed: true,\n            currentUsage: {\n                current: currentTokens,\n                limit: profile.monthly_token_limit,\n                remaining: profile.monthly_token_limit - currentTokens - tokensToUse\n            }\n        };\n    } catch (error) {\n        console.error('Error validating token limits:', error);\n        return {\n            allowed: false,\n            reason: 'Error de validación'\n        };\n    }\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario\n */ async function getTokenUsageStats(userId) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        const { data: profile, error } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error || !profile) {\n            return null;\n        }\n        // Reset si es nuevo mes\n        let currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n        const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n        const percentage = profile.monthly_token_limit > 0 ? currentTokens / profile.monthly_token_limit * 100 : 0;\n        return {\n            currentMonth: {\n                used: currentTokens,\n                limit: profile.monthly_token_limit,\n                percentage: Math.round(percentage),\n                remaining: profile.monthly_token_limit - currentTokens\n            },\n            plan: {\n                name: planConfig?.name || profile.subscription_plan,\n                features: planConfig?.features || []\n            },\n            paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n        };\n    } catch (error) {\n        console.error('Error getting token usage stats:', error);\n        return null;\n    }\n}\n/**\n * Actualiza los límites de tokens cuando cambia el plan del usuario\n */ async function updateUserPlanLimits(userId, newPlan) {\n    try {\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const newTokenLimit = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(newPlan);\n        const { error } = await supabase.from('user_profiles').update({\n            subscription_plan: newPlan,\n            monthly_token_limit: newTokenLimit,\n            updated_at: new Date().toISOString()\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error updating user plan limits:', error);\n            return false;\n        }\n        console.log(`✅ Plan actualizado para usuario ${userId}: ${newPlan} (${newTokenLimit} tokens)`);\n        return true;\n    } catch (error) {\n        console.error('Error updating user plan limits:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/tokenUsageService.server.ts\n");

/***/ })

};
;