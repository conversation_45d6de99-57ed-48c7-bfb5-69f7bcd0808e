"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_tokenUsageService_ts";
exports.ids = ["_rsc_src_lib_supabase_tokenUsageService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            autoRefreshToken: true,\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUksTUFBTTtZQUNKQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsb0JBQW9CLEtBQVEsOENBQThDO1FBQzVFO0lBQ0Y7QUFFSjtBQUVBLCtDQUErQztBQUN4QyxNQUFNQyxXQUFXVCxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLCAgICAgICAvLyBQZXJzaXN0aXIgc2VzacOzbiBlbiBlbCBuYXZlZ2Fkb3JcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSwgICAgIC8vIFJlZnJlc2NhciB0b2tlbiBhdXRvbcOhdGljYW1lbnRlXG4gICAgICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSAgICAvLyBFU0VOQ0lBTDogRGV0ZWN0YXIgeSBwcm9jZXNhciB0b2tlbnMgZGUgVVJMXG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/tokenUsageService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canPerformActivity: () => (/* binding */ canPerformActivity),\n/* harmony export */   checkTokenLimit: () => (/* binding */ checkTokenLimit),\n/* harmony export */   getTokenPurchaseHistory: () => (/* binding */ getTokenPurchaseHistory),\n/* harmony export */   getTokenUsageProgress: () => (/* binding */ getTokenUsageProgress),\n/* harmony export */   getUserPlanInfo: () => (/* binding */ getUserPlanInfo),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserTokenStats: () => (/* binding */ getUserTokenStats),\n/* harmony export */   saveTokenUsage: () => (/* binding */ saveTokenUsage)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(rsc)/./src/config/index.ts\");\n\n\n/**\n * Guarda el uso de tokens en Supabase con validación de plan\n */ async function saveTokenUsage(data) {\n    try {\n        console.log('🔄 saveTokenUsage (cliente) iniciado con data:', data);\n        // Este servicio solo funciona en el cliente\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        console.log('✅ Cliente Supabase creado');\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');\n        if (userError || !user) {\n            console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);\n            return;\n        }\n        // Validar que el usuario tiene acceso a la actividad\n        const accessValidation = await validateActivityAccess(user.id, data.activity, data.usage.totalTokens);\n        if (!accessValidation.allowed) {\n            console.warn('❌ Acceso denegado para actividad:', accessValidation.reason);\n            throw new Error(accessValidation.reason);\n        }\n        const usageRecord = {\n            user_id: user.id,\n            activity_type: data.activity,\n            model_name: data.model,\n            prompt_tokens: data.usage.promptTokens,\n            completion_tokens: data.usage.completionTokens,\n            total_tokens: data.usage.totalTokens,\n            estimated_cost: data.usage.estimatedCost || 0,\n            usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n        };\n        console.log('📝 Registro a insertar:', usageRecord);\n        const { error } = await supabase.from('user_token_usage').insert([\n            usageRecord\n        ]);\n        if (error) {\n            console.error('❌ Error al guardar uso de tokens:', error);\n            return;\n        }\n        console.log('✅ Registro insertado exitosamente en user_token_usage');\n        // Actualizar contador mensual del usuario\n        await updateMonthlyTokenCount(user.id, data.usage.totalTokens);\n    } catch (error) {\n        console.error('Error en saveTokenUsage:', error);\n    }\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ async function updateMonthlyTokenCount(userId, tokens) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Obtener o crear perfil del usuario\n        let { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (profileError && profileError.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', profileError);\n            return;\n        }\n        if (!profile) {\n            // Crear perfil nuevo\n            const { error: insertError } = await supabase.from('user_profiles').insert([\n                {\n                    user_id: userId,\n                    subscription_plan: 'free',\n                    monthly_token_limit: _config__WEBPACK_IMPORTED_MODULE_1__.TOKEN_LIMITS.DEFAULT_FREE_LIMIT,\n                    current_month_tokens: tokens,\n                    current_month: currentMonth\n                }\n            ]);\n            if (insertError) {\n                console.error('Error al crear perfil:', insertError);\n            }\n        } else {\n            // Actualizar perfil existente\n            const newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n            const { error: updateError } = await supabase.from('user_profiles').update({\n                current_month_tokens: newTokenCount,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', userId);\n            if (updateError) {\n                console.error('Error al actualizar perfil:', updateError);\n            }\n        }\n    } catch (error) {\n        console.error('Error en updateMonthlyTokenCount:', error);\n    }\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario actual\n */ async function getUserTokenStats() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return getEmptyStats();\n        }\n        const { data: records, error } = await supabase.from('user_token_usage').select('*').eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener estadísticas:', error);\n            return getEmptyStats();\n        }\n        return calculateStats(records || []);\n    } catch (error) {\n        console.error('Error en getUserTokenStats:', error);\n        return getEmptyStats();\n    }\n}\n/**\n * Calcula estadísticas a partir de los registros\n */ function calculateStats(records) {\n    const stats = {\n        totalSessions: records.length,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n    records.forEach((record)=>{\n        const tokens = record.total_tokens;\n        const cost = record.estimated_cost;\n        stats.totalTokens += tokens;\n        stats.totalCost += cost;\n        // Por actividad\n        if (!stats.byActivity[record.activity_type]) {\n            stats.byActivity[record.activity_type] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byActivity[record.activity_type].tokens += tokens;\n        stats.byActivity[record.activity_type].cost += cost;\n        stats.byActivity[record.activity_type].count += 1;\n        // Por modelo\n        if (!stats.byModel[record.model_name]) {\n            stats.byModel[record.model_name] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byModel[record.model_name].tokens += tokens;\n        stats.byModel[record.model_name].cost += cost;\n        stats.byModel[record.model_name].count += 1;\n    });\n    return stats;\n}\n/**\n * Retorna estadísticas vacías\n */ function getEmptyStats() {\n    return {\n        totalSessions: 0,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n}\n/**\n * Obtiene el perfil del usuario actual\n */ async function getUserProfile() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        const { data: profile, error } = await supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error al obtener perfil:', error);\n            return null;\n        }\n        return profile;\n    } catch (error) {\n        console.error('Error en getUserProfile:', error);\n        return null;\n    }\n}\n/**\n * Verifica si el usuario ha alcanzado su límite mensual\n */ async function checkTokenLimit() {\n    try {\n        const profile = await getUserProfile();\n        if (!profile) {\n            return {\n                hasReachedLimit: false,\n                currentTokens: 0,\n                limit: _config__WEBPACK_IMPORTED_MODULE_1__.TOKEN_LIMITS.DEFAULT_FREE_LIMIT,\n                percentage: 0\n            };\n        }\n        const currentTokens = profile.current_month_tokens || 0;\n        const monthlyLimit = profile.monthly_token_limit || 0;\n        const percentage = monthlyLimit > 0 ? currentTokens / monthlyLimit * 100 : 0;\n        const hasReachedLimit = currentTokens >= monthlyLimit;\n        return {\n            hasReachedLimit,\n            currentTokens,\n            limit: monthlyLimit,\n            percentage\n        };\n    } catch (error) {\n        console.error('Error en checkTokenLimit:', error);\n        return {\n            hasReachedLimit: false,\n            currentTokens: 0,\n            limit: _config__WEBPACK_IMPORTED_MODULE_1__.TOKEN_LIMITS.DEFAULT_FREE_LIMIT,\n            percentage: 0\n        };\n    }\n}\n/**\n * Valida si un usuario tiene acceso a una actividad específica\n */ async function validateActivityAccess(userId, activity, tokensToUse) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Obtener perfil del usuario\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                allowed: false,\n                reason: _config__WEBPACK_IMPORTED_MODULE_1__.ERROR_MESSAGES.PROFILE_NOT_FOUND\n            };\n        }\n        // Mapear actividades a características usando configuración centralizada\n        const featureName = _config__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP[activity] || activity;\n        // Verificar acceso a la característica según el plan\n        if (!(0,_config__WEBPACK_IMPORTED_MODULE_1__.hasFeatureAccess)(profile.subscription_plan, featureName)) {\n            return {\n                allowed: false,\n                reason: `La actividad ${activity} no está disponible en el plan ${profile.subscription_plan}`\n            };\n        }\n        // Verificar pago para planes de pago\n        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                allowed: false,\n                reason: 'Pago no verificado. Complete el proceso de pago para usar esta función.'\n            };\n        }\n        // Verificar límites de tokens\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        let currentTokens = profile.current_month_tokens;\n        // Reset si es nuevo mes\n        if (profile.current_month !== currentMonth) {\n            currentTokens = 0;\n        }\n        if (currentTokens + tokensToUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual de tokens alcanzado. Usado: ${currentTokens}/${profile.monthly_token_limit}`\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error validating activity access:', error);\n        return {\n            allowed: false,\n            reason: 'Error interno de validación'\n        };\n    }\n}\n/**\n * Obtiene información detallada del plan del usuario\n */ async function getUserPlanInfo() {\n    try {\n        const profile = await getUserProfile();\n        if (!profile) {\n            return null;\n        }\n        const planConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n        if (!planConfig) {\n            return null;\n        }\n        const currentTokens = profile.current_month_tokens || 0;\n        const monthlyLimit = profile.monthly_token_limit || 0;\n        const percentage = monthlyLimit > 0 ? currentTokens / monthlyLimit * 100 : 0;\n        return {\n            plan: profile.subscription_plan,\n            planName: planConfig.name,\n            features: planConfig.features,\n            tokenUsage: {\n                current: currentTokens,\n                limit: monthlyLimit,\n                percentage: Math.round(percentage),\n                remaining: Math.max(0, monthlyLimit - currentTokens)\n            },\n            paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n        };\n    } catch (error) {\n        console.error('Error getting user plan info:', error);\n        return null;\n    }\n}\n/**\n * Verifica si el usuario puede realizar una actividad específica antes de ejecutarla\n */ async function canPerformActivity(activity, estimatedTokens = 0) {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return {\n                allowed: false,\n                reason: 'Usuario no autenticado'\n            };\n        }\n        const validation = await validateActivityAccess(user.id, activity, estimatedTokens);\n        if (!validation.allowed) {\n            const planInfo = await getUserPlanInfo();\n            return {\n                allowed: false,\n                reason: validation.reason,\n                planInfo\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking activity permission:', error);\n        return {\n            allowed: false,\n            reason: 'Error interno de validación'\n        };\n    }\n}\n/**\n * Obtiene datos de progreso de tokens para estadísticas avanzadas\n */ async function getTokenUsageProgress() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        // Obtener perfil del usuario\n        const profile = await getUserProfile();\n        if (!profile) {\n            return null;\n        }\n        // Calcular porcentaje de uso\n        const percentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n        // Obtener historial diario de los últimos 30 días\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const { data: dailyUsage, error: historyError } = await supabase.from('user_token_usage').select('created_at, total_tokens').eq('user_id', user.id).gte('created_at', thirtyDaysAgo.toISOString()).order('created_at', {\n            ascending: true\n        });\n        if (historyError) {\n            console.error('Error al obtener historial diario:', historyError);\n        }\n        // Agrupar por día\n        const dailyHistory = [];\n        const dailyMap = new Map();\n        if (dailyUsage) {\n            dailyUsage.forEach((record)=>{\n                const date = new Date(record.created_at).toISOString().split('T')[0];\n                const currentTokens = dailyMap.get(date) || 0;\n                dailyMap.set(date, currentTokens + record.total_tokens);\n            });\n            // Convertir a array ordenado\n            for(let i = 29; i >= 0; i--){\n                const date = new Date();\n                date.setDate(date.getDate() - i);\n                const dateStr = date.toISOString().split('T')[0];\n                dailyHistory.push({\n                    date: dateStr,\n                    tokens: dailyMap.get(dateStr) || 0\n                });\n            }\n        }\n        return {\n            percentage: Math.round(percentage),\n            limit: profile.monthly_token_limit,\n            used: profile.current_month_tokens,\n            remaining: profile.monthly_token_limit - profile.current_month_tokens,\n            dailyHistory\n        };\n    } catch (error) {\n        console.error('Error en getTokenUsageProgress:', error);\n        return null;\n    }\n}\n/**\n * Obtiene historial de compras de tokens del usuario\n */ async function getTokenPurchaseHistory() {\n    try {\n        const supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            return null;\n        }\n        const { data: purchases, error } = await supabase.from('token_purchases').select('id, amount, price, created_at, status').eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de compras:', error);\n            return null;\n        }\n        return purchases || [];\n    } catch (error) {\n        console.error('Error en getTokenPurchaseHistory:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/tokenUsageService.ts\n");

/***/ })

};
;