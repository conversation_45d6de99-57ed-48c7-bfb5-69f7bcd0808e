// src/lib/services/stripeWebhookHandlers.ts
// Manejadores específicos para eventos de webhooks de Stripe

import Stripe from 'stripe';
import { stripe } from '@/lib/stripe/config';
import { UserManagementService } from './userManagement';
import { SupabaseAdminService, supabaseAdmin } from '@/lib/supabase/admin';
import { EmailNotificationService } from './email/emailNotificationService';
import { randomUUID } from 'crypto';
import { getTokenLimitForPlan } from '../../config/plans';

export interface WebhookHandlerResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class StripeWebhookHandlers {
  
  /**
   * Manejar evento checkout.session.completed
   * Se ejecuta cuando un pago se completa exitosamente
   */
  static async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<WebhookHandlerResult> {
    try {
      console.log('🎯 Procesando checkout.session.completed:', session.id);

      // Validar que el pago esté completado
      if (session.payment_status !== 'paid') {
        return {
          success: false,
          message: 'Payment not completed',
          error: `Payment status: ${session.payment_status}`
        };
      }

      // Verificar si es una compra de tokens
      if (session.metadata?.type === 'token_purchase') {
        return await this.handleTokenPurchase(session);
      }

      // Extraer metadata para creación de usuario
      const { planId, customerEmail, customerName } = session.metadata || {};

      if (!planId || !customerEmail) {
        return {
          success: false,
          message: 'Missing required metadata',
          error: 'planId and customerEmail are required'
        };
      }

      // Obtener el ID de la suscripción si el modo es 'subscription'
      const subscriptionId = session.mode === 'subscription' ? session.subscription as string : undefined;

      // Verificar si ya fue procesado
      const existingTransaction = await SupabaseAdminService.getTransactionBySessionId(session.id);
      if (existingTransaction) {
        console.log('⚠️ Transacción ya procesada:', existingTransaction.id);
        return {
          success: true,
          message: 'Transaction already processed',
          data: { transactionId: existingTransaction.id }
        };
      }

      // Verificar si ya existe un usuario con este customer ID (para evitar duplicados)
      const { data: existingProfile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan')
        .eq('stripe_customer_id', session.customer)
        .single();

      if (existingProfile) {
        console.log('⚠️ Usuario ya existe para este customer ID:', session.customer);
        // Si es una suscripción nueva para un usuario existente, actualizar el plan
        if (subscriptionId) {
          const result = await UserManagementService.updateUserPlan(
            existingProfile.user_id,
            planId,
            undefined,
            'New subscription for existing customer'
          );

          if (result.success) {
            return {
              success: true,
              message: 'Existing user plan updated with new subscription',
              data: { userId: existingProfile.user_id }
            };
          }
        }

        return {
          success: false,
          message: 'Customer already exists but plan update failed'
        };
      }

      // Para suscripciones, obtener current_period_end directamente de Stripe
      let planExpiresAt = null;
      if (subscriptionId && stripe) {
        try {
          const subscription = await stripe.subscriptions.retrieve(subscriptionId);
          planExpiresAt = (subscription as any).current_period_end
            ? new Date((subscription as any).current_period_end * 1000).toISOString()
            : null;
          console.log(`[handleCheckoutSessionCompleted] Plan expires at from subscription: ${planExpiresAt}`);
        } catch (error) {
          console.error('[handleCheckoutSessionCompleted] Error obteniendo suscripción:', error);
        }
      }

      // Verificar si hay datos de registro en los metadatos (flujo legacy)
      const registrationData = session.metadata?.registrationData;
      const preRegisteredUserId = session.metadata?.userId;

      let result;
      if (preRegisteredUserId) {
        // NUEVO FLUJO: Usuario ya pre-registrado, activar y enviar email de confirmación
        console.log(`🆕 Activando usuario pre-registrado después del pago: ${preRegisteredUserId}`);

        try {
          // 1. Crear transacción de Stripe
          const transaction = await SupabaseAdminService.createStripeTransaction({
            stripe_session_id: session.id,
            stripe_customer_id: session.customer as string,
            user_email: customerEmail,
            user_name: customerName,
            plan_id: planId,
            amount: session.amount_total || 0,
            currency: session.currency || 'eur',
            payment_status: 'paid',
            subscription_id: subscriptionId,
            user_id: preRegisteredUserId,
            metadata: {
              created_by: 'webhook',
              activation_flow: 'pre_registered_user'
            }
          });

          // 2. Actualizar perfil de usuario con datos de pago
          const { error: updateError } = await supabaseAdmin
            .from('user_profiles')
            .update({
              payment_verified: true,
              stripe_customer_id: session.customer as string,
              subscription_plan: planId,
              monthly_token_limit: getTokenLimitForPlan(planId),
              stripe_subscription_id: subscriptionId,
              last_payment_date: new Date().toISOString(),
              plan_expires_at: planExpiresAt,
              auto_renew: subscriptionId ? true : false,
              updated_at: new Date().toISOString(),
              security_flags: {
                payment_completed: true,
                payment_date: new Date().toISOString(),
                stripe_session_id: session.id,
                subscription_id: subscriptionId,
                pre_registered: false, // Ya no está en estado pendiente
                activated: true
              }
            })
            .eq('user_id', preRegisteredUserId);

          if (updateError) {
            console.error('Error actualizando usuario pre-registrado:', updateError);
            result = { success: false, error: updateError.message };
          } else {
            // 3. Enviar email de confirmación AHORA que el pago está completado
            console.log('📧 Enviando email de confirmación después del pago exitoso...');

            const emailResult = await SupabaseAdminService.sendConfirmationEmailForUser(preRegisteredUserId);

            if (!emailResult.success) {
              console.error('⚠️ Error enviando email de confirmación:', emailResult.error);
              // No fallar completamente, el usuario puede confirmar manualmente
            } else {
              console.log('✅ Email de confirmación enviado exitosamente después del pago');
            }

            // 5. Activar transacción
            await SupabaseAdminService.activateTransaction(transaction.id);

            result = {
              success: true,
              userId: preRegisteredUserId,
              transactionId: transaction.id,
              activated: true
            };
          }

        } catch (activationError) {
          console.error('Error activando usuario pre-registrado:', activationError);
          result = { success: false, error: activationError instanceof Error ? activationError.message : 'Activation error' };
        }

      } else if (registrationData) {
        // FLUJO LEGACY: Crear cuenta después del pago exitoso (mantener compatibilidad)
        console.log(`🔄 Flujo legacy: Creando cuenta después del pago exitoso para: ${customerEmail}`);

        try {
          const regData = JSON.parse(registrationData);

          // Crear usuario con plan usando los datos de registro
          result = await UserManagementService.createUserWithPlan({
            email: regData.email,
            password: regData.password, // Contraseña del formulario
            name: regData.customerName,
            planId: regData.planId,
            stripeSessionId: session.id,
            stripeCustomerId: session.customer as string,
            amount: session.amount_total || 0,
            currency: session.currency || 'eur',
            subscriptionId: subscriptionId,
            planExpiresAt: planExpiresAt,
            sendConfirmationEmail: true // ENVIAR EMAIL DESPUÉS DEL PAGO
          });

        } catch (parseError) {
          console.error('Error parseando datos de registro:', parseError);
          result = { success: false, error: 'Invalid registration data format' };
        }

      } else {
        // Flujo original: crear usuario con plan
        result = await UserManagementService.createUserWithPlan({
          email: customerEmail,
          name: customerName,
          planId: planId,
          stripeSessionId: session.id,
          stripeCustomerId: session.customer as string,
          amount: session.amount_total || 0,
          currency: session.currency || 'eur',
          subscriptionId: subscriptionId,
          planExpiresAt: planExpiresAt // Pasar la fecha de expiración
        });
      }

      if (!result.success) {
        // Si el error es porque el email ya existe, intentar un flujo de actualización
        const isEmailExistsError = result.error && (
          result.error.includes('A user with this email address has already been registered') ||
          result.error.includes('email_exists') ||
          result.error.includes('already been registered') ||
          result.error.includes('User already registered')
        );

        if (isEmailExistsError) {
          // ===== INICIO DE LA MODIFICACIÓN DE SEGURIDAD =====
          console.log(`📧 Email existente detectado. Iniciando flujo de confirmación para: ${customerEmail}`);
          console.log(`🔒 [SECURITY] Potential plan upgrade attempt for existing email: ${customerEmail}, planId: ${planId}, sessionId: ${session.id}`);

          try {
            // 1. Obtener el User ID del propietario de la cuenta
            const existingUser = await SupabaseAdminService.getUserByEmail(customerEmail);
            if (!existingUser) {
              // Esto no debería ocurrir si isEmailExistsError es verdadero, pero es una buena salvaguarda.
              console.error(`🚨 [SECURITY] Usuario existente no encontrado a pesar del error de duplicado: ${customerEmail}`);
              return {
                success: false,
                message: 'Failed to retrieve existing user by email.',
                error: 'User not found despite email_exists error'
              };
            }

            // 2. Crear un token de confirmación único y seguro.
            const confirmationToken = randomUUID();
            const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Token expira en 24h

            console.log(`🔑 [SECURITY] Generated confirmation token for user ${existingUser.id}, expires at: ${expiresAt.toISOString()}`);

            // 3. Guardar el intento de actualización pendiente en la metadata de la transacción.
            const transaction = await SupabaseAdminService.getTransactionBySessionId(session.id);
            if (transaction) {
              await supabaseAdmin.from('stripe_transactions').update({
                metadata: {
                  ...transaction.metadata,
                  pending_upgrade: {
                    userId: existingUser.id,
                    newPlanId: planId,
                    confirmationToken: confirmationToken,
                    tokenExpiresAt: expiresAt.toISOString(),
                    status: 'pending_confirmation',
                    createdAt: new Date().toISOString(),
                    customerEmail: customerEmail,
                    customerName: customerName,
                    stripeCustomerId: session.customer as string,
                    stripeSubscriptionId: subscriptionId
                  }
                }
              }).eq('id', transaction.id);

              console.log(`💾 [SECURITY] Saved pending upgrade data for transaction: ${transaction.id}`);
            }

            // 4. Enviar email de confirmación al propietario de la cuenta (Usuario B)
            const emailSent = await EmailNotificationService.sendPlanUpgradeConfirmationEmail(
              customerEmail, // Email del propietario de la cuenta
              customerName || 'Usuario',
              planId,
              confirmationToken
            );

            if (!emailSent) {
              console.error(`📧 [SECURITY] Failed to send confirmation email to: ${customerEmail}`);
              // No fallar el webhook por esto, pero registrar el error
            } else {
              console.log(`✅ [SECURITY] Confirmation email sent successfully to: ${customerEmail}`);
            }

            // 5. Enviar alerta de seguridad al administrador
            const alertSent = await EmailNotificationService.sendSecurityAlert({
              payerEmail: session.customer_details?.email || 'unknown',
              accountOwnerEmail: customerEmail,
              requestedPlan: planId,
              currentPlan: 'unknown',
              paymentAmount: session.amount_total || 0,
              currency: session.currency || 'eur',
              stripeSessionId: session.id,
              timestamp: new Date().toISOString(),
              actionTaken: `Email de confirmación enviado a ${customerEmail}. Upgrade pendiente de confirmación del usuario.`
            });

            if (!alertSent) {
              console.error(`🚨 [SECURITY] Failed to send security alert to administrator`);
            } else {
              console.log(`✅ [SECURITY] Security alert sent successfully to administrator`);
            }

            // 6. Retornar una respuesta de éxito al webhook, indicando que el proceso está pendiente.
            return {
              success: true,
              message: 'Existing user detected. Confirmation email sent for plan upgrade.',
              data: {
                status: 'pending_confirmation',
                userId: existingUser.id,
                confirmationRequired: true,
                emailSent: emailSent
              }
            };

          } catch (confirmationError) {
             console.error('❌ [SECURITY] Error en el flujo de confirmación de upgrade:', confirmationError);
             return {
               success: false,
               message: 'Failed to process plan upgrade confirmation flow.',
               error: confirmationError instanceof Error ? confirmationError.message : 'Unknown confirmation error'
             };
          }
          // ===== FIN DE LA MODIFICACIÓN DE SEGURIDAD =====
        }

        // Si es otro tipo de error
        return {
          success: false,
          message: 'Failed to create user',
          error: result.error
        };
      }

      console.log('✅ Usuario creado exitosamente desde webhook');

      return {
        success: true,
        message: 'User created successfully',
        data: {
          userId: result.userId,
          profileId: result.profileId,
          transactionId: result.transactionId
        }
      };

    } catch (error) {
      console.error('❌ Error en handleCheckoutSessionCompleted:', error);
      return {
        success: false,
        message: 'Internal error processing checkout session',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Manejar compra de tokens adicionales
   */
  private static async handleTokenPurchase(session: Stripe.Checkout.Session): Promise<WebhookHandlerResult> {
    try {
      console.log('🪙 Procesando compra de tokens:', session.id);

      const { user_id, token_amount, price } = session.metadata || {};

      if (!user_id || !token_amount || !price) {
        return {
          success: false,
          message: 'Missing required metadata for token purchase',
          error: 'user_id, token_amount, and price are required'
        };
      }

      // Verificar si ya fue procesado
      const { data: existingPurchase } = await supabaseAdmin
        .from('token_purchases')
        .select('id')
        .eq('transaction_id', session.id)
        .single();

      if (existingPurchase) {
        console.log('⚠️ Compra de tokens ya procesada:', existingPurchase.id);
        return {
          success: true,
          message: 'Token purchase already processed',
          data: { purchaseId: existingPurchase.id }
        };
      }

      const tokenAmount = parseInt(token_amount);
      const purchasePrice = parseFloat(price);

      // Registrar la compra de tokens
      const { data: purchase, error: purchaseError } = await supabaseAdmin
        .from('token_purchases')
        .insert([{
          user_id: user_id,
          amount: tokenAmount,
          price: purchasePrice,
          transaction_id: session.id,
          status: 'completed'
        }])
        .select()
        .single();

      if (purchaseError) {
        console.error('Error registrando compra de tokens:', purchaseError);
        return {
          success: false,
          message: 'Error registering token purchase',
          error: purchaseError.message
        };
      }

      // Actualizar límite de tokens del usuario
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('user_profiles')
        .select('monthly_token_limit')
        .eq('user_id', user_id)
        .single();

      if (profileError || !profile) {
        console.error('Error obteniendo perfil de usuario:', profileError);
        return {
          success: false,
          message: 'User profile not found',
          error: profileError?.message || 'Profile not found'
        };
      }

      const newTokenLimit = profile.monthly_token_limit + tokenAmount;

      const { error: updateError } = await supabaseAdmin
        .from('user_profiles')
        .update({
          monthly_token_limit: newTokenLimit,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user_id);

      if (updateError) {
        console.error('Error actualizando límite de tokens:', updateError);
        return {
          success: false,
          message: 'Error updating token limit',
          error: updateError.message
        };
      }

      console.log('✅ Compra de tokens procesada exitosamente:', {
        userId: user_id,
        tokensAdded: tokenAmount,
        newLimit: newTokenLimit,
        purchaseId: purchase.id
      });

      return {
        success: true,
        message: 'Token purchase processed successfully',
        data: {
          purchaseId: purchase.id,
          tokensAdded: tokenAmount,
          newTokenLimit: newTokenLimit
        }
      };

    } catch (error) {
      console.error('❌ Error en handleTokenPurchase:', error);
      return {
        success: false,
        message: 'Internal error processing token purchase',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento payment_intent.succeeded
   * Para pagos únicos exitosos
   */
  static async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<WebhookHandlerResult> {
    try {
      console.log('💳 Procesando payment_intent.succeeded:', paymentIntent.id);
      
      // Para pagos únicos, la lógica principal está en checkout.session.completed
      // Aquí solo registramos el evento para auditoría
      
      return {
        success: true,
        message: 'Payment intent logged successfully',
        data: { paymentIntentId: paymentIntent.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handlePaymentIntentSucceeded:', error);
      return {
        success: false,
        message: 'Error processing payment intent',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.created
   * Cuando se crea una nueva suscripción
   */
  static async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('🔄 Procesando customer.subscription.created:', subscription.id);

      // Obtener información del cliente
      const customerId = subscription.customer as string;
      const planId = subscription.metadata?.planId;

      if (!planId) {
        return {
          success: false,
          message: 'Missing plan ID in subscription metadata'
        };
      }

      // Actualizar información de suscripción en la transacción
      const { error: transactionError } = await supabaseAdmin
        .from('stripe_transactions')
        .update({
          subscription_id: subscription.id,
          metadata: {
            subscription_status: subscription.status,
            subscription_id: subscription.id,
            updated_at: new Date().toISOString()
          }
        })
        .eq('stripe_customer_id', customerId);

      if (transactionError) {
        console.error('Error actualizando transacción con suscripción:', transactionError);
      }

      // Establecer plan_expires_at inicial usando current_period_end de la suscripción
      const planExpiresAt = (subscription as any).current_period_end
        ? new Date((subscription as any).current_period_end * 1000).toISOString()
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días

      console.log(`[handleSubscriptionCreated] Actualizando perfil para customerId: ${customerId}`);
      console.log(`[handleSubscriptionCreated] stripe_subscription_id: ${subscription.id}, plan_expires_at: ${planExpiresAt}`);

      // Intentar actualizar por stripe_customer_id primero
      let { error: profileError, data: updatedProfileData } = await supabaseAdmin
        .from('user_profiles')
        .update({
          stripe_subscription_id: subscription.id,
          plan_expires_at: planExpiresAt,
          auto_renew: true,
          last_payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          security_flags: {
            subscription_status: subscription.status,
            subscription_id: subscription.id,
            subscription_created_at: new Date().toISOString()
          }
        })
        .eq('stripe_customer_id', customerId)
        .select();

      // Si no se encontró por stripe_customer_id, buscar por email en los metadatos
      if (!updatedProfileData || updatedProfileData.length === 0) {
        console.log(`[handleSubscriptionCreated] No se encontró perfil por customerId, buscando por email...`);

        const customerEmail = subscription.metadata?.customerEmail;
        if (customerEmail) {
          // Obtener user_id por email
          const existingUser = await SupabaseAdminService.getUserByEmail(customerEmail);
          if (existingUser) {
            console.log(`[handleSubscriptionCreated] Actualizando perfil por user_id: ${existingUser.id}`);

            const updateResult = await supabaseAdmin
              .from('user_profiles')
              .update({
                stripe_customer_id: customerId, // Establecer el customer_id que faltaba
                stripe_subscription_id: subscription.id,
                plan_expires_at: planExpiresAt,
                auto_renew: true,
                last_payment_date: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                security_flags: {
                  subscription_status: subscription.status,
                  subscription_id: subscription.id,
                  subscription_created_at: new Date().toISOString()
                }
              })
              .eq('user_id', existingUser.id)
              .select();

            profileError = updateResult.error;
            updatedProfileData = updateResult.data;
          }
        }
      }

      if (profileError) {
        console.error('[handleSubscriptionCreated] Error actualizando perfil con suscripción:', profileError);
      } else {
        console.log('[handleSubscriptionCreated] Perfil actualizado con datos de suscripción:', updatedProfileData);
      }

      return {
        success: true,
        message: 'Subscription created successfully',
        data: { subscriptionId: subscription.id, planExpiresAt }
      };

    } catch (error) {
      console.error('❌ Error en handleSubscriptionCreated:', error);
      return {
        success: false,
        message: 'Error processing subscription creation',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.updated
   * Cuando se actualiza una suscripción
   */
  static async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('🔄 Procesando customer.subscription.updated:', subscription.id);
      
      // Obtener usuario por customer ID
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan')
        .eq('stripe_customer_id', subscription.customer)
        .single();
      
      if (!profile) {
        return {
          success: false,
          message: 'User profile not found for customer'
        };
      }
      
      // Actualizar estado de la suscripción usando current_period_end de Stripe
      const planExpiresAt = (subscription as any).current_period_end
        ? new Date((subscription as any).current_period_end * 1000).toISOString()
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días

      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          plan_expires_at: planExpiresAt,
          updated_at: new Date().toISOString(),
          security_flags: {
            subscription_status: subscription.status,
            subscription_id: subscription.id,
            last_updated: new Date().toISOString()
          }
        })
        .eq('user_id', profile.user_id);
      
      if (error) {
        console.error('Error actualizando perfil de usuario:', error);
        return {
          success: false,
          message: 'Error updating user profile'
        };
      }
      
      return {
        success: true,
        message: 'Subscription updated successfully',
        data: { subscriptionId: subscription.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleSubscriptionUpdated:', error);
      return {
        success: false,
        message: 'Error processing subscription update',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento customer.subscription.deleted
   * Cuando se cancela una suscripción - implementa período de gracia
   */
  static async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<WebhookHandlerResult> {
    try {
      console.log('❌ Procesando customer.subscription.deleted:', subscription.id);

      // Obtener usuario por customer ID
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan, plan_expires_at')
        .eq('stripe_customer_id', subscription.customer)
        .single();

      if (!profile) {
        return {
          success: false,
          message: 'User profile not found for customer'
        };
      }

      // Obtener current_period_end de la suscripción para período de gracia
      const gracePeriodEnd = (subscription as any).current_period_end
        ? new Date((subscription as any).current_period_end * 1000).toISOString()
        : new Date().toISOString(); // Si no hay current_period_end, expirar inmediatamente

      console.log(`🕐 Período de gracia hasta: ${gracePeriodEnd} para usuario: ${profile.user_id}`);

      // En lugar de degradar inmediatamente, mantener plan actual hasta current_period_end
      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update({
          plan_expires_at: gracePeriodEnd,
          auto_renew: false, // Cancelar auto-renovación
          stripe_subscription_id: null, // Limpiar ID de suscripción
          updated_at: new Date().toISOString(),
          security_flags: {
            subscription_cancelled: true,
            cancellation_date: new Date().toISOString(),
            grace_period_until: gracePeriodEnd,
            cancelled_subscription_id: subscription.id,
            last_updated: new Date().toISOString()
          }
        })
        .eq('user_id', profile.user_id);

      if (error) {
        console.error('Error actualizando perfil para período de gracia:', error);
        return {
          success: false,
          message: 'Error setting up grace period',
          error: error.message
        };
      }

      // Registrar el cambio en el historial
      await SupabaseAdminService.logPlanChange({
        user_id: profile.user_id,
        old_plan: profile.subscription_plan,
        new_plan: profile.subscription_plan, // Mantener el mismo plan durante gracia
        changed_by: 'system',
        reason: `Subscription cancelled - Grace period until ${gracePeriodEnd}`
      });

      // Obtener información del usuario para enviar notificación por email
      try {
        const { data: userData } = await supabaseAdmin.auth.admin.getUserById(profile.user_id);
        if (userData.user?.email) {
          const userName = userData.user.user_metadata?.name || userData.user.email.split('@')[0];
          const planName = profile.subscription_plan === 'usuario' ? 'Usuario' : 'Pro';

          // Enviar notificación de cancelación con período de gracia
          await EmailNotificationService.sendSubscriptionCancelledNotification(
            userData.user.email,
            userName,
            planName,
            gracePeriodEnd,
            profile.user_id
          );

          console.log(`📧 Notificación de cancelación enviada a: ${userData.user.email}`);
        }
      } catch (emailError) {
        console.error('Error enviando notificación de cancelación:', emailError);
        // No fallar el webhook por error de email
      }

      return {
        success: true,
        message: `Subscription cancelled with grace period until ${gracePeriodEnd}`,
        data: {
          userId: profile.user_id,
          gracePeriodEnd,
          currentPlan: profile.subscription_plan
        }
      };

    } catch (error) {
      console.error('❌ Error en handleSubscriptionDeleted:', error);
      return {
        success: false,
        message: 'Error processing subscription deletion',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Manejar evento invoice.payment_succeeded
   * Para renovaciones de suscripción
   */
  static async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<WebhookHandlerResult> {
    try {
      console.log('💰 Procesando invoice.payment_succeeded:', invoice.id);
      
      // Verificar si la factura está relacionada con una suscripción
      const subscriptionId = (invoice as any).subscription;
      if (!subscriptionId) {
        return {
          success: true,
          message: 'Invoice not related to subscription, skipping'
        };
      }
      
      // Obtener información de la suscripción para actualizar plan_expires_at
      let planExpiresAt = null;
      try {
        if (subscriptionId && stripe) {
          const subscription = await stripe.subscriptions.retrieve(subscriptionId);
          planExpiresAt = (subscription as any).current_period_end
            ? new Date((subscription as any).current_period_end * 1000).toISOString()
            : null;
        }
      } catch (error) {
        console.error('Error obteniendo información de suscripción:', error);
      }

      // Obtener perfil del usuario para determinar si es renovación
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, subscription_plan, current_month_tokens, monthly_token_limit')
        .eq('stripe_customer_id', invoice.customer)
        .single();

      // Actualizar fecha de último pago y plan_expires_at
      const updateData: any = {
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (planExpiresAt) {
        updateData.plan_expires_at = planExpiresAt;

        // Para suscripciones, resetear tokens al inicio del nuevo período de facturación
        if (profile && (profile.subscription_plan === 'usuario' || profile.subscription_plan === 'pro')) {
          updateData.current_month_tokens = 0;
          updateData.current_month = new Date().toISOString().slice(0, 7) + '-01';
          console.log('🔄 Reseteando tokens para renovación de suscripción:', profile.user_id);
        }
      }

      const { error } = await supabaseAdmin
        .from('user_profiles')
        .update(updateData)
        .eq('stripe_customer_id', invoice.customer);
      
      if (error) {
        console.error('Error actualizando fecha de pago:', error);
      }
      
      return {
        success: true,
        message: 'Invoice payment processed successfully',
        data: { invoiceId: invoice.id }
      };
      
    } catch (error) {
      console.error('❌ Error en handleInvoicePaymentSucceeded:', error);
      return {
        success: false,
        message: 'Error processing invoice payment',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
