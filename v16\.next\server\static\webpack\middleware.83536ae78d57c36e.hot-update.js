"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n// ===== Archivo: src/middleware.ts (VERSIÓN FINAL Y RECOMENDADA) =====\n\n\nasync function middleware(request) {\n    let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: {\n            headers: request.headers\n        }\n    });\n    // Crea un cliente de Supabase para este request específico.\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                request.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                request.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n                response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value: '',\n                    ...options\n                });\n            }\n        }\n    });\n    // Esta llamada es la más importante. `getSession()` (o `getUser()`)\n    // lee las cookies, refresca la sesión si es necesario, y actualiza las\n    // cookies en el objeto 'response' para mantener todo sincronizado.\n    await supabase.auth.getSession();\n    // Devuelve la respuesta, que ahora contiene las cookies de sesión actualizadas.\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\n     * Coincide con todas las rutas de petición excepto las de archivos estáticos\n     * y de optimización de imágenes.\n     */ '/((?!_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});