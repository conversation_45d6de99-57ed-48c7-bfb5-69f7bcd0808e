/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/notifications/route";
exports.ids = ["app/api/user/notifications/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fnotifications%2Froute&page=%2Fapi%2Fuser%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fnotifications%2Froute&page=%2Fapi%2Fuser%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_user_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user/notifications/route.ts */ \"(rsc)/./src/app/api/user/notifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/notifications/route\",\n        pathname: \"/api/user/notifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/notifications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\user\\\\notifications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_user_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fnotifications%2Froute&page=%2Fapi%2Fuser%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user/notifications/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/user/notifications/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _lib_services_email_emailNotificationService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/email/emailNotificationService */ \"(rsc)/./src/lib/services/email/emailNotificationService.ts\");\n// src/app/api/user/notifications/route.ts\n// Endpoint para que usuarios consulten su historial de notificaciones\n\n\n\nasync function GET(request) {\n    try {\n        // Verificar autenticación del usuario\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll () {}\n            }\n        });\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obtener parámetros de consulta\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const type = searchParams.get('type') || undefined;\n        // Obtener notificaciones del usuario\n        const result = await _lib_services_email_emailNotificationService__WEBPACK_IMPORTED_MODULE_2__.EmailNotificationService.getUserNotifications(user.id, limit, type);\n        // Formatear respuesta para el frontend\n        const formattedNotifications = result.notifications.map((notification)=>({\n                id: notification.id,\n                type: notification.type,\n                subject: notification.subject,\n                sentAt: notification.sent_at,\n                status: notification.status,\n                metadata: notification.metadata\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                notifications: formattedNotifications,\n                total: result.total,\n                hasMore: result.total > limit\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('❌ Error obteniendo notificaciones del usuario:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Error interno del servidor',\n            details: error instanceof Error ? error.message : 'Error desconocido'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user/notifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailAnalytics.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailAnalytics.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailAnalytics: () => (/* binding */ EmailAnalytics)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailAnalytics.ts\n// Análisis y estadísticas de notificaciones por email\n\nclass EmailAnalytics {\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const byType = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.type] = (acc[notif.type] || 0) + 1;\n                return acc;\n            }, {});\n            const byStatus = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.status] = (acc[notif.status] || 0) + 1;\n                return acc;\n            }, {});\n            const recentNotifications = (notifications || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10);\n            return {\n                byType,\n                byStatus,\n                total: notifications?.length || 0,\n                recentNotifications\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de notificaciones:', error);\n            return {\n                byType: {},\n                byStatus: {},\n                total: 0,\n                recentNotifications: []\n            };\n        }\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: failures, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones para calcular tasa de fallo\n            let totalQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            });\n            if (startDate) {\n                totalQuery = totalQuery.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                totalQuery = totalQuery.lte('sent_at', endDate);\n            }\n            const { count: totalCount } = await totalQuery;\n            // Agrupar errores por tipo\n            const errorsByType = (failures || []).reduce((acc, failure)=>{\n                const errorMessage = failure.metadata?.error_message || 'Unknown error';\n                const errorType = this.categorizeError(errorMessage);\n                acc[errorType] = (acc[errorType] || 0) + 1;\n                return acc;\n            }, {});\n            const totalFailures = failures?.length || 0;\n            const failureRate = totalCount && totalCount > 0 ? totalFailures / totalCount * 100 : 0;\n            return {\n                totalFailures,\n                failureRate: Math.round(failureRate * 100) / 100,\n                errorsByType,\n                recentFailures: (failures || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10).map((failure)=>({\n                        id: failure.id,\n                        type: failure.type,\n                        recipient: failure.recipient_email,\n                        error: failure.metadata?.error_message || 'Unknown error',\n                        failedAt: failure.metadata?.failed_at || failure.sent_at\n                    }))\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de fallos:', error);\n            return {\n                totalFailures: 0,\n                failureRate: 0,\n                errorsByType: {},\n                recentFailures: []\n            };\n        }\n    }\n    /**\n   * Categorizar errores para estadísticas\n   */ static categorizeError(errorMessage) {\n        const message = errorMessage.toLowerCase();\n        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {\n            return 'Network Error';\n        }\n        if (message.includes('invalid') || message.includes('malformed') || message.includes('email')) {\n            return 'Invalid Email';\n        }\n        if (message.includes('rate limit') || message.includes('quota') || message.includes('limit')) {\n            return 'Rate Limit';\n        }\n        if (message.includes('auth') || message.includes('key') || message.includes('permission')) {\n            return 'Authentication Error';\n        }\n        if (message.includes('bounce') || message.includes('reject')) {\n            return 'Email Bounced';\n        }\n        return 'Other Error';\n    }\n    /**\n   * Obtener métricas de rendimiento por período\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const totalSent = notifications?.length || 0;\n            const successful = notifications?.filter((n)=>n.status === 'sent').length || 0;\n            const successRate = totalSent > 0 ? successful / totalSent * 100 : 0;\n            // Agrupar por hora del día para encontrar picos\n            const peakHours = (notifications || []).reduce((acc, notif)=>{\n                const hour = new Date(notif.sent_at).getHours();\n                acc[hour] = (acc[hour] || 0) + 1;\n                return acc;\n            }, {});\n            // Agrupar por día\n            const dailyVolume = (notifications || []).reduce((acc, notif)=>{\n                const day = notif.sent_at.split('T')[0];\n                acc[day] = (acc[day] || 0) + 1;\n                return acc;\n            }, {});\n            return {\n                totalSent,\n                successRate: Math.round(successRate * 100) / 100,\n                avgResponseTime: 0,\n                peakHours,\n                dailyVolume\n            };\n        } catch (error) {\n            console.error('Error obteniendo métricas de rendimiento:', error);\n            return {\n                totalSent: 0,\n                successRate: 0,\n                avgResponseTime: 0,\n                peakHours: {},\n                dailyVolume: {}\n            };\n        }\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('user_id, recipient_email, sent_at').not('user_id', 'is', null);\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Agrupar por usuario\n            const userStats = (notifications || []).reduce((acc, notif)=>{\n                const userId = notif.user_id;\n                if (!acc[userId]) {\n                    acc[userId] = {\n                        userId,\n                        email: notif.recipient_email,\n                        count: 0,\n                        lastNotification: notif.sent_at\n                    };\n                }\n                acc[userId].count++;\n                if (new Date(notif.sent_at) > new Date(acc[userId].lastNotification)) {\n                    acc[userId].lastNotification = notif.sent_at;\n                }\n                return acc;\n            }, {});\n            // Convertir a array y ordenar por count\n            return Object.values(userStats).sort((a, b)=>b.count - a.count).slice(0, limit);\n        } catch (error) {\n            console.error('Error obteniendo top usuarios:', error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailAnalytics.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailLogger.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailLogger.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailLogger: () => (/* binding */ EmailLogger)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailLogger.ts\n// Logging y tracking de notificaciones en base de datos\n\nclass EmailLogger {\n    /**\n   * Registrar notificación en base de datos para tracking\n   */ static async logEmailNotification(notification, status = 'sent') {\n        try {\n            const insertData = {\n                recipient_email: notification.to,\n                subject: notification.subject,\n                type: notification.type,\n                sent_at: new Date().toISOString(),\n                status: status\n            };\n            // Agregar user_id si está disponible\n            if (notification.userId) {\n                insertData.user_id = notification.userId;\n            }\n            // Agregar metadata si está disponible\n            if (notification.metadata) {\n                insertData.metadata = notification.metadata;\n            }\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').insert(insertData).select('id').single();\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification logged:', {\n                id: data.id,\n                type: notification.type,\n                recipient: notification.to,\n                status: status,\n                userId: notification.userId || 'N/A'\n            });\n            return data.id;\n        } catch (error) {\n            console.error('Error logging email notification:', error);\n            // No lanzar error, es solo para tracking\n            return null;\n        }\n    }\n    /**\n   * Actualizar estado de una notificación existente\n   */ static async updateEmailNotificationStatus(notificationId, status, errorMessage) {\n        try {\n            const updateData = {\n                status: status,\n                updated_at: new Date().toISOString()\n            };\n            // Si es un fallo, agregar el mensaje de error a metadata\n            if (status === 'failed' && errorMessage) {\n                updateData.metadata = {\n                    error_message: errorMessage,\n                    failed_at: new Date().toISOString()\n                };\n            }\n            // Si es exitoso, marcar como entregado\n            if (status === 'sent') {\n                updateData.delivered_at = new Date().toISOString();\n            }\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').update(updateData).eq('id', notificationId);\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification status updated:', {\n                id: notificationId,\n                status: status,\n                error: errorMessage || 'N/A'\n            });\n        } catch (error) {\n            console.error('Error updating email notification status:', error);\n        // No lanzar error, es solo para tracking\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('user_id', userId).order('sent_at', {\n                ascending: false\n            });\n            if (type) {\n                query = query.eq('type', type);\n            }\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones\n            let countQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId);\n            if (type) {\n                countQuery = countQuery.eq('type', type);\n            }\n            const { count } = await countQuery;\n            return {\n                notifications: notifications || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error('Error obteniendo notificaciones del usuario:', error);\n            return {\n                notifications: [],\n                total: 0\n            };\n        }\n    }\n    /**\n   * Obtener notificaciones fallidas para reintentos\n   */ static async getFailedNotifications(maxAge = 24, limit = 10 // Máximo 10 por consulta\n    ) {\n        try {\n            const cutoffDate = new Date(Date.now() - maxAge * 60 * 60 * 1000).toISOString();\n            const { data: failedNotifications, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed').gte('sent_at', cutoffDate).limit(limit);\n            if (error) {\n                throw error;\n            }\n            return failedNotifications || [];\n        } catch (error) {\n            console.error('Error obteniendo notificaciones fallidas:', error);\n            return [];\n        }\n    }\n    /**\n   * Marcar notificación como reintentada\n   */ static async markAsRetried(originalNotificationId, success, errorMessage) {\n        try {\n            const status = success ? 'retried_successfully' : 'failed';\n            const message = success ? 'Successfully retried' : errorMessage || 'Retry failed';\n            await this.updateEmailNotificationStatus(originalNotificationId, status, message);\n        } catch (error) {\n            console.error('Error marcando notificación como reintentada:', error);\n        }\n    }\n    /**\n   * Limpiar notificaciones antiguas (para mantenimiento)\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        try {\n            const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();\n            console.log(`🧹 Limpiando notificaciones anteriores a: ${cutoffDate}`);\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').delete().lt('sent_at', cutoffDate).select('id');\n            if (error) {\n                throw error;\n            }\n            const deletedCount = data?.length || 0;\n            console.log(`✅ Limpieza completada: ${deletedCount} notificaciones eliminadas`);\n            return {\n                deleted: deletedCount\n            };\n        } catch (error) {\n            console.error('Error en limpieza de notificaciones:', error);\n            return {\n                deleted: 0,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2VtYWlsL2VtYWlsTG9nZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsd0NBQXdDO0FBQ3hDLHdEQUF3RDtBQUVIO0FBRzlDLE1BQU1DO0lBRVg7O0dBRUMsR0FDRCxhQUFhQyxxQkFDWEMsWUFBK0IsRUFDL0JDLFNBQXNCLE1BQU0sRUFDSjtRQUN4QixJQUFJO1lBQ0YsTUFBTUMsYUFBMkI7Z0JBQy9CQyxpQkFBaUJILGFBQWFJLEVBQUU7Z0JBQ2hDQyxTQUFTTCxhQUFhSyxPQUFPO2dCQUM3QkMsTUFBTU4sYUFBYU0sSUFBSTtnQkFDdkJDLFNBQVMsSUFBSUMsT0FBT0MsV0FBVztnQkFDL0JSLFFBQVFBO1lBQ1Y7WUFFQSxxQ0FBcUM7WUFDckMsSUFBSUQsYUFBYVUsTUFBTSxFQUFFO2dCQUN2QlIsV0FBV1MsT0FBTyxHQUFHWCxhQUFhVSxNQUFNO1lBQzFDO1lBRUEsc0NBQXNDO1lBQ3RDLElBQUlWLGFBQWFZLFFBQVEsRUFBRTtnQkFDekJWLFdBQVdVLFFBQVEsR0FBR1osYUFBYVksUUFBUTtZQUM3QztZQUVBLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNakIsOERBQWFBLENBQ3hDa0IsSUFBSSxDQUFDLHVCQUNMQyxNQUFNLENBQUNkLFlBQ1BlLE1BQU0sQ0FBQyxNQUNQQyxNQUFNO1lBRVQsSUFBSUosT0FBTztnQkFDVCxNQUFNQTtZQUNSO1lBRUFLLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUM7Z0JBQzNDQyxJQUFJUixLQUFLUSxFQUFFO2dCQUNYZixNQUFNTixhQUFhTSxJQUFJO2dCQUN2QmdCLFdBQVd0QixhQUFhSSxFQUFFO2dCQUMxQkgsUUFBUUE7Z0JBQ1JTLFFBQVFWLGFBQWFVLE1BQU0sSUFBSTtZQUNqQztZQUVBLE9BQU9HLEtBQUtRLEVBQUU7UUFDaEIsRUFBRSxPQUFPUCxPQUFPO1lBQ2RLLFFBQVFMLEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELHlDQUF5QztZQUN6QyxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYVMsOEJBQ1hDLGNBQXNCLEVBQ3RCdkIsTUFBbUIsRUFDbkJ3QixZQUFxQixFQUNOO1FBQ2YsSUFBSTtZQUNGLE1BQU1DLGFBQThCO2dCQUNsQ3pCLFFBQVFBO2dCQUNSMEIsWUFBWSxJQUFJbkIsT0FBT0MsV0FBVztZQUNwQztZQUVBLHlEQUF5RDtZQUN6RCxJQUFJUixXQUFXLFlBQVl3QixjQUFjO2dCQUN2Q0MsV0FBV2QsUUFBUSxHQUFHO29CQUNwQmdCLGVBQWVIO29CQUNmSSxXQUFXLElBQUlyQixPQUFPQyxXQUFXO2dCQUNuQztZQUNGO1lBRUEsdUNBQXVDO1lBQ3ZDLElBQUlSLFdBQVcsUUFBUTtnQkFDckJ5QixXQUFXSSxZQUFZLEdBQUcsSUFBSXRCLE9BQU9DLFdBQVc7WUFDbEQ7WUFFQSxNQUFNLEVBQUVLLEtBQUssRUFBRSxHQUFHLE1BQU1qQiw4REFBYUEsQ0FDbENrQixJQUFJLENBQUMsdUJBQ0xnQixNQUFNLENBQUNMLFlBQ1BNLEVBQUUsQ0FBQyxNQUFNUjtZQUVaLElBQUlWLE9BQU87Z0JBQ1QsTUFBTUE7WUFDUjtZQUVBSyxRQUFRQyxHQUFHLENBQUMseUNBQXlDO2dCQUNuREMsSUFBSUc7Z0JBQ0p2QixRQUFRQTtnQkFDUmEsT0FBT1csZ0JBQWdCO1lBQ3pCO1FBRUYsRUFBRSxPQUFPWCxPQUFPO1lBQ2RLLFFBQVFMLEtBQUssQ0FBQyw2Q0FBNkNBO1FBQzNELHlDQUF5QztRQUMzQztJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhbUIscUJBQ1h2QixNQUFjLEVBQ2R3QixRQUFnQixFQUFFLEVBQ2xCNUIsSUFBYSxFQUNxQjtRQUNsQyxJQUFJO1lBQ0YsSUFBSTZCLFFBQVF0Qyw4REFBYUEsQ0FDdEJrQixJQUFJLENBQUMsdUJBQ0xFLE1BQU0sQ0FBQyxLQUNQZSxFQUFFLENBQUMsV0FBV3RCLFFBQ2QwQixLQUFLLENBQUMsV0FBVztnQkFBRUMsV0FBVztZQUFNO1lBRXZDLElBQUkvQixNQUFNO2dCQUNSNkIsUUFBUUEsTUFBTUgsRUFBRSxDQUFDLFFBQVExQjtZQUMzQjtZQUVBLElBQUk0QixPQUFPO2dCQUNUQyxRQUFRQSxNQUFNRCxLQUFLLENBQUNBO1lBQ3RCO1lBRUEsTUFBTSxFQUFFckIsTUFBTXlCLGFBQWEsRUFBRXhCLEtBQUssRUFBRSxHQUFHLE1BQU1xQjtZQUU3QyxJQUFJckIsT0FBTztnQkFDVCxNQUFNQTtZQUNSO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUl5QixhQUFhMUMsOERBQWFBLENBQzNCa0IsSUFBSSxDQUFDLHVCQUNMRSxNQUFNLENBQUMsS0FBSztnQkFBRXVCLE9BQU87Z0JBQVNDLE1BQU07WUFBSyxHQUN6Q1QsRUFBRSxDQUFDLFdBQVd0QjtZQUVqQixJQUFJSixNQUFNO2dCQUNSaUMsYUFBYUEsV0FBV1AsRUFBRSxDQUFDLFFBQVExQjtZQUNyQztZQUVBLE1BQU0sRUFBRWtDLEtBQUssRUFBRSxHQUFHLE1BQU1EO1lBRXhCLE9BQU87Z0JBQ0xELGVBQWVBLGlCQUFpQixFQUFFO2dCQUNsQ0ksT0FBT0YsU0FBUztZQUNsQjtRQUVGLEVBQUUsT0FBTzFCLE9BQU87WUFDZEssUUFBUUwsS0FBSyxDQUFDLGdEQUFnREE7WUFDOUQsT0FBTztnQkFDTHdCLGVBQWUsRUFBRTtnQkFDakJJLE9BQU87WUFDVDtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFDLHVCQUNYQyxTQUFpQixFQUFFLEVBQ25CVixRQUFnQixHQUFLLHlCQUF5QjtJQUE1QixFQUNGO1FBQ2hCLElBQUk7WUFDRixNQUFNVyxhQUFhLElBQUlyQyxLQUFLQSxLQUFLc0MsR0FBRyxLQUFLRixTQUFTLEtBQUssS0FBSyxNQUFNbkMsV0FBVztZQUU3RSxNQUFNLEVBQUVJLE1BQU1rQyxtQkFBbUIsRUFBRWpDLEtBQUssRUFBRSxHQUFHLE1BQU1qQiw4REFBYUEsQ0FDN0RrQixJQUFJLENBQUMsdUJBQ0xFLE1BQU0sQ0FBQyxLQUNQZSxFQUFFLENBQUMsVUFBVSxVQUNiZ0IsR0FBRyxDQUFDLFdBQVdILFlBQ2ZYLEtBQUssQ0FBQ0E7WUFFVCxJQUFJcEIsT0FBTztnQkFDVCxNQUFNQTtZQUNSO1lBRUEsT0FBT2lDLHVCQUF1QixFQUFFO1FBRWxDLEVBQUUsT0FBT2pDLE9BQU87WUFDZEssUUFBUUwsS0FBSyxDQUFDLDZDQUE2Q0E7WUFDM0QsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYW1DLGNBQ1hDLHNCQUE4QixFQUM5QkMsT0FBZ0IsRUFDaEIxQixZQUFxQixFQUNOO1FBQ2YsSUFBSTtZQUNGLE1BQU14QixTQUFTa0QsVUFBVSx5QkFBeUI7WUFDbEQsTUFBTUMsVUFBVUQsVUFBVSx5QkFBeUIxQixnQkFBZ0I7WUFFbkUsTUFBTSxJQUFJLENBQUNGLDZCQUE2QixDQUN0QzJCLHdCQUNBakQsUUFDQW1EO1FBR0osRUFBRSxPQUFPdEMsT0FBTztZQUNkSyxRQUFRTCxLQUFLLENBQUMsaURBQWlEQTtRQUNqRTtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhdUMsd0JBQ1hDLGFBQXFCLEVBQUUsRUFJdEI7UUFDRCxJQUFJO1lBQ0YsTUFBTVQsYUFBYSxJQUFJckMsS0FBS0EsS0FBS3NDLEdBQUcsS0FBS1EsYUFBYSxLQUFLLEtBQUssS0FBSyxNQUFNN0MsV0FBVztZQUV0RlUsUUFBUUMsR0FBRyxDQUFDLENBQUMsMENBQTBDLEVBQUV5QixZQUFZO1lBRXJFLE1BQU0sRUFBRWhDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWpCLDhEQUFhQSxDQUN4Q2tCLElBQUksQ0FBQyx1QkFDTHdDLE1BQU0sR0FDTkMsRUFBRSxDQUFDLFdBQVdYLFlBQ2Q1QixNQUFNLENBQUM7WUFFVixJQUFJSCxPQUFPO2dCQUNULE1BQU1BO1lBQ1I7WUFFQSxNQUFNMkMsZUFBZTVDLE1BQU02QyxVQUFVO1lBQ3JDdkMsUUFBUUMsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVxQyxhQUFhLDBCQUEwQixDQUFDO1lBRTlFLE9BQU87Z0JBQ0xFLFNBQVNGO1lBQ1g7UUFFRixFQUFFLE9BQU8zQyxPQUFPO1lBQ2RLLFFBQVFMLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3RELE9BQU87Z0JBQ0w2QyxTQUFTO2dCQUNUN0MsT0FBT0EsaUJBQWlCOEMsUUFBUTlDLE1BQU1zQyxPQUFPLEdBQUc7WUFDbEQ7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxsaWJcXHNlcnZpY2VzXFxlbWFpbFxcZW1haWxMb2dnZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2xpYi9zZXJ2aWNlcy9lbWFpbC9lbWFpbExvZ2dlci50c1xuLy8gTG9nZ2luZyB5IHRyYWNraW5nIGRlIG5vdGlmaWNhY2lvbmVzIGVuIGJhc2UgZGUgZGF0b3NcblxuaW1wb3J0IHsgc3VwYWJhc2VBZG1pbiB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL2FkbWluJztcbmltcG9ydCB7IEVtYWlsTm90aWZpY2F0aW9uLCBFbWFpbExvZ0RhdGEsIEVtYWlsVXBkYXRlRGF0YSwgVXNlck5vdGlmaWNhdGlvbnNSZXN1bHQsIEVtYWlsU3RhdHVzIH0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCBjbGFzcyBFbWFpbExvZ2dlciB7XG4gIFxuICAvKipcbiAgICogUmVnaXN0cmFyIG5vdGlmaWNhY2nDs24gZW4gYmFzZSBkZSBkYXRvcyBwYXJhIHRyYWNraW5nXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgbG9nRW1haWxOb3RpZmljYXRpb24oXG4gICAgbm90aWZpY2F0aW9uOiBFbWFpbE5vdGlmaWNhdGlvbiwgXG4gICAgc3RhdHVzOiBFbWFpbFN0YXR1cyA9ICdzZW50J1xuICApOiBQcm9taXNlPHN0cmluZyB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgaW5zZXJ0RGF0YTogRW1haWxMb2dEYXRhID0ge1xuICAgICAgICByZWNpcGllbnRfZW1haWw6IG5vdGlmaWNhdGlvbi50byxcbiAgICAgICAgc3ViamVjdDogbm90aWZpY2F0aW9uLnN1YmplY3QsXG4gICAgICAgIHR5cGU6IG5vdGlmaWNhdGlvbi50eXBlLFxuICAgICAgICBzZW50X2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHN0YXR1czogc3RhdHVzXG4gICAgICB9O1xuXG4gICAgICAvLyBBZ3JlZ2FyIHVzZXJfaWQgc2kgZXN0w6EgZGlzcG9uaWJsZVxuICAgICAgaWYgKG5vdGlmaWNhdGlvbi51c2VySWQpIHtcbiAgICAgICAgaW5zZXJ0RGF0YS51c2VyX2lkID0gbm90aWZpY2F0aW9uLnVzZXJJZDtcbiAgICAgIH1cblxuICAgICAgLy8gQWdyZWdhciBtZXRhZGF0YSBzaSBlc3TDoSBkaXNwb25pYmxlXG4gICAgICBpZiAobm90aWZpY2F0aW9uLm1ldGFkYXRhKSB7XG4gICAgICAgIGluc2VydERhdGEubWV0YWRhdGEgPSBub3RpZmljYXRpb24ubWV0YWRhdGE7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgICAgLmZyb20oJ2VtYWlsX25vdGlmaWNhdGlvbnMnKVxuICAgICAgICAuaW5zZXJ0KGluc2VydERhdGEpXG4gICAgICAgIC5zZWxlY3QoJ2lkJylcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgICAgIFxuICAgICAgY29uc29sZS5sb2coJ/Cfk50gRW1haWwgbm90aWZpY2F0aW9uIGxvZ2dlZDonLCB7XG4gICAgICAgIGlkOiBkYXRhLmlkLFxuICAgICAgICB0eXBlOiBub3RpZmljYXRpb24udHlwZSxcbiAgICAgICAgcmVjaXBpZW50OiBub3RpZmljYXRpb24udG8sXG4gICAgICAgIHN0YXR1czogc3RhdHVzLFxuICAgICAgICB1c2VySWQ6IG5vdGlmaWNhdGlvbi51c2VySWQgfHwgJ04vQSdcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gZGF0YS5pZDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9nZ2luZyBlbWFpbCBub3RpZmljYXRpb246JywgZXJyb3IpO1xuICAgICAgLy8gTm8gbGFuemFyIGVycm9yLCBlcyBzb2xvIHBhcmEgdHJhY2tpbmdcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBBY3R1YWxpemFyIGVzdGFkbyBkZSB1bmEgbm90aWZpY2FjacOzbiBleGlzdGVudGVcbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVFbWFpbE5vdGlmaWNhdGlvblN0YXR1cyhcbiAgICBub3RpZmljYXRpb25JZDogc3RyaW5nLFxuICAgIHN0YXR1czogRW1haWxTdGF0dXMsXG4gICAgZXJyb3JNZXNzYWdlPzogc3RyaW5nXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cGRhdGVEYXRhOiBFbWFpbFVwZGF0ZURhdGEgPSB7XG4gICAgICAgIHN0YXR1czogc3RhdHVzLFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH07XG5cbiAgICAgIC8vIFNpIGVzIHVuIGZhbGxvLCBhZ3JlZ2FyIGVsIG1lbnNhamUgZGUgZXJyb3IgYSBtZXRhZGF0YVxuICAgICAgaWYgKHN0YXR1cyA9PT0gJ2ZhaWxlZCcgJiYgZXJyb3JNZXNzYWdlKSB7XG4gICAgICAgIHVwZGF0ZURhdGEubWV0YWRhdGEgPSB7XG4gICAgICAgICAgZXJyb3JfbWVzc2FnZTogZXJyb3JNZXNzYWdlLFxuICAgICAgICAgIGZhaWxlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIC8vIFNpIGVzIGV4aXRvc28sIG1hcmNhciBjb21vIGVudHJlZ2Fkb1xuICAgICAgaWYgKHN0YXR1cyA9PT0gJ3NlbnQnKSB7XG4gICAgICAgIHVwZGF0ZURhdGEuZGVsaXZlcmVkX2F0ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKCdlbWFpbF9ub3RpZmljYXRpb25zJylcbiAgICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgICAuZXEoJ2lkJywgbm90aWZpY2F0aW9uSWQpO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIEVtYWlsIG5vdGlmaWNhdGlvbiBzdGF0dXMgdXBkYXRlZDonLCB7XG4gICAgICAgIGlkOiBub3RpZmljYXRpb25JZCxcbiAgICAgICAgc3RhdHVzOiBzdGF0dXMsXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UgfHwgJ04vQSdcbiAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGVtYWlsIG5vdGlmaWNhdGlvbiBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgLy8gTm8gbGFuemFyIGVycm9yLCBlcyBzb2xvIHBhcmEgdHJhY2tpbmdcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogT2J0ZW5lciBoaXN0b3JpYWwgZGUgbm90aWZpY2FjaW9uZXMgZGUgdW4gdXN1YXJpb1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFVzZXJOb3RpZmljYXRpb25zKFxuICAgIHVzZXJJZDogc3RyaW5nLFxuICAgIGxpbWl0OiBudW1iZXIgPSA1MCxcbiAgICB0eXBlPzogc3RyaW5nXG4gICk6IFByb21pc2U8VXNlck5vdGlmaWNhdGlvbnNSZXN1bHQ+IHtcbiAgICB0cnkge1xuICAgICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbSgnZW1haWxfbm90aWZpY2F0aW9ucycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAgIC5vcmRlcignc2VudF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KTtcblxuICAgICAgaWYgKHR5cGUpIHtcbiAgICAgICAgcXVlcnkgPSBxdWVyeS5lcSgndHlwZScsIHR5cGUpO1xuICAgICAgfVxuXG4gICAgICBpZiAobGltaXQpIHtcbiAgICAgICAgcXVlcnkgPSBxdWVyeS5saW1pdChsaW1pdCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogbm90aWZpY2F0aW9ucywgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5O1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG5cbiAgICAgIC8vIE9idGVuZXIgdG90YWwgZGUgbm90aWZpY2FjaW9uZXNcbiAgICAgIGxldCBjb3VudFF1ZXJ5ID0gc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbSgnZW1haWxfbm90aWZpY2F0aW9ucycpXG4gICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZCk7XG5cbiAgICAgIGlmICh0eXBlKSB7XG4gICAgICAgIGNvdW50UXVlcnkgPSBjb3VudFF1ZXJ5LmVxKCd0eXBlJywgdHlwZSk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgY291bnQgfSA9IGF3YWl0IGNvdW50UXVlcnk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIG5vdGlmaWNhdGlvbnM6IG5vdGlmaWNhdGlvbnMgfHwgW10sXG4gICAgICAgIHRvdGFsOiBjb3VudCB8fCAwXG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIG9idGVuaWVuZG8gbm90aWZpY2FjaW9uZXMgZGVsIHVzdWFyaW86JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbm90aWZpY2F0aW9uczogW10sXG4gICAgICAgIHRvdGFsOiAwXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRlbmVyIG5vdGlmaWNhY2lvbmVzIGZhbGxpZGFzIHBhcmEgcmVpbnRlbnRvc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEZhaWxlZE5vdGlmaWNhdGlvbnMoXG4gICAgbWF4QWdlOiBudW1iZXIgPSAyNCwgLy8gTcOheGltbyAyNCBob3JhcyBkZSBhbnRpZ8O8ZWRhZFxuICAgIGxpbWl0OiBudW1iZXIgPSAxMCAgIC8vIE3DoXhpbW8gMTAgcG9yIGNvbnN1bHRhXG4gICk6IFByb21pc2U8YW55W10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY3V0b2ZmRGF0ZSA9IG5ldyBEYXRlKERhdGUubm93KCkgLSBtYXhBZ2UgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKTtcbiAgICAgIFxuICAgICAgY29uc3QgeyBkYXRhOiBmYWlsZWROb3RpZmljYXRpb25zLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbSgnZW1haWxfbm90aWZpY2F0aW9ucycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ3N0YXR1cycsICdmYWlsZWQnKVxuICAgICAgICAuZ3RlKCdzZW50X2F0JywgY3V0b2ZmRGF0ZSlcbiAgICAgICAgLmxpbWl0KGxpbWl0KTtcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gZmFpbGVkTm90aWZpY2F0aW9ucyB8fCBbXTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBvYnRlbmllbmRvIG5vdGlmaWNhY2lvbmVzIGZhbGxpZGFzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTWFyY2FyIG5vdGlmaWNhY2nDs24gY29tbyByZWludGVudGFkYVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIG1hcmtBc1JldHJpZWQoXG4gICAgb3JpZ2luYWxOb3RpZmljYXRpb25JZDogc3RyaW5nLFxuICAgIHN1Y2Nlc3M6IGJvb2xlYW4sXG4gICAgZXJyb3JNZXNzYWdlPzogc3RyaW5nXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdGF0dXMgPSBzdWNjZXNzID8gJ3JldHJpZWRfc3VjY2Vzc2Z1bGx5JyA6ICdmYWlsZWQnO1xuICAgICAgY29uc3QgbWVzc2FnZSA9IHN1Y2Nlc3MgPyAnU3VjY2Vzc2Z1bGx5IHJldHJpZWQnIDogZXJyb3JNZXNzYWdlIHx8ICdSZXRyeSBmYWlsZWQnO1xuXG4gICAgICBhd2FpdCB0aGlzLnVwZGF0ZUVtYWlsTm90aWZpY2F0aW9uU3RhdHVzKFxuICAgICAgICBvcmlnaW5hbE5vdGlmaWNhdGlvbklkLFxuICAgICAgICBzdGF0dXMsXG4gICAgICAgIG1lc3NhZ2VcbiAgICAgICk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbWFyY2FuZG8gbm90aWZpY2FjacOzbiBjb21vIHJlaW50ZW50YWRhOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTGltcGlhciBub3RpZmljYWNpb25lcyBhbnRpZ3VhcyAocGFyYSBtYW50ZW5pbWllbnRvKVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNsZWFudXBPbGROb3RpZmljYXRpb25zKFxuICAgIGRheXNUb0tlZXA6IG51bWJlciA9IDkwXG4gICk6IFByb21pc2U8e1xuICAgIGRlbGV0ZWQ6IG51bWJlcjtcbiAgICBlcnJvcj86IHN0cmluZztcbiAgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBjdXRvZmZEYXRlID0gbmV3IERhdGUoRGF0ZS5ub3coKSAtIGRheXNUb0tlZXAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpO1xuICAgICAgXG4gICAgICBjb25zb2xlLmxvZyhg8J+nuSBMaW1waWFuZG8gbm90aWZpY2FjaW9uZXMgYW50ZXJpb3JlcyBhOiAke2N1dG9mZkRhdGV9YCk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgICAgLmZyb20oJ2VtYWlsX25vdGlmaWNhdGlvbnMnKVxuICAgICAgICAuZGVsZXRlKClcbiAgICAgICAgLmx0KCdzZW50X2F0JywgY3V0b2ZmRGF0ZSlcbiAgICAgICAgLnNlbGVjdCgnaWQnKTtcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkZWxldGVkQ291bnQgPSBkYXRhPy5sZW5ndGggfHwgMDtcbiAgICAgIGNvbnNvbGUubG9nKGDinIUgTGltcGllemEgY29tcGxldGFkYTogJHtkZWxldGVkQ291bnR9IG5vdGlmaWNhY2lvbmVzIGVsaW1pbmFkYXNgKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgZGVsZXRlZDogZGVsZXRlZENvdW50XG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGVuIGxpbXBpZXphIGRlIG5vdGlmaWNhY2lvbmVzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGRlbGV0ZWQ6IDAsXG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ1xuICAgICAgfTtcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZUFkbWluIiwiRW1haWxMb2dnZXIiLCJsb2dFbWFpbE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsInN0YXR1cyIsImluc2VydERhdGEiLCJyZWNpcGllbnRfZW1haWwiLCJ0byIsInN1YmplY3QiLCJ0eXBlIiwic2VudF9hdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVzZXJJZCIsInVzZXJfaWQiLCJtZXRhZGF0YSIsImRhdGEiLCJlcnJvciIsImZyb20iLCJpbnNlcnQiLCJzZWxlY3QiLCJzaW5nbGUiLCJjb25zb2xlIiwibG9nIiwiaWQiLCJyZWNpcGllbnQiLCJ1cGRhdGVFbWFpbE5vdGlmaWNhdGlvblN0YXR1cyIsIm5vdGlmaWNhdGlvbklkIiwiZXJyb3JNZXNzYWdlIiwidXBkYXRlRGF0YSIsInVwZGF0ZWRfYXQiLCJlcnJvcl9tZXNzYWdlIiwiZmFpbGVkX2F0IiwiZGVsaXZlcmVkX2F0IiwidXBkYXRlIiwiZXEiLCJnZXRVc2VyTm90aWZpY2F0aW9ucyIsImxpbWl0IiwicXVlcnkiLCJvcmRlciIsImFzY2VuZGluZyIsIm5vdGlmaWNhdGlvbnMiLCJjb3VudFF1ZXJ5IiwiY291bnQiLCJoZWFkIiwidG90YWwiLCJnZXRGYWlsZWROb3RpZmljYXRpb25zIiwibWF4QWdlIiwiY3V0b2ZmRGF0ZSIsIm5vdyIsImZhaWxlZE5vdGlmaWNhdGlvbnMiLCJndGUiLCJtYXJrQXNSZXRyaWVkIiwib3JpZ2luYWxOb3RpZmljYXRpb25JZCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiY2xlYW51cE9sZE5vdGlmaWNhdGlvbnMiLCJkYXlzVG9LZWVwIiwiZGVsZXRlIiwibHQiLCJkZWxldGVkQ291bnQiLCJsZW5ndGgiLCJkZWxldGVkIiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailLogger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailNotificationService.ts":
/*!************************************************************!*\
  !*** ./src/lib/services/email/emailNotificationService.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailNotificationService: () => (/* binding */ EmailNotificationService)\n/* harmony export */ });\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/services/email/emailTemplates.ts\");\n/* harmony import */ var _emailSender__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailSender */ \"(rsc)/./src/lib/services/email/emailSender.ts\");\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\n/* harmony import */ var _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./emailAnalytics */ \"(rsc)/./src/lib/services/email/emailAnalytics.ts\");\n// src/lib/services/email/emailNotificationService.ts\n// Servicio principal de notificaciones por email (API pública)\n\n\n\n\nclass EmailNotificationService {\n    /**\n   * Enviar notificación de cancelación de suscripción\n   */ static async sendSubscriptionCancelledNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'subscription_cancelled',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    daysRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación de cancelación:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar recordatorio de que el período de gracia está por terminar\n   */ static async sendGracePeriodEndingNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'grace_period_ending',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    hoursRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando recordatorio de período de gracia:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar notificación genérica\n   */ static async sendGenericNotification(userEmail, userName, title, message, type = 'other', userId, ctaText, ctaUrl) {\n        try {\n            // Generar contenido del email usando template genérico\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGenericEmail(userName, title, message, ctaText, ctaUrl);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type,\n                userId,\n                metadata: {\n                    userName,\n                    title,\n                    message,\n                    ctaText,\n                    ctaUrl\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación genérica:', error);\n            return false;\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.getUserNotifications(userId, limit, type);\n    }\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getNotificationStats(startDate, endDate);\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getFailureStats(startDate, endDate);\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, limit = 10) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.retryFailedNotifications(maxAge, limit);\n    }\n    /**\n   * Obtener métricas de rendimiento\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getPerformanceMetrics(startDate, endDate);\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getTopUsersByVolume(limit, startDate, endDate);\n    }\n    /**\n   * Enviar email de prueba\n   */ static async sendTestEmail(to, providerConfig) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendTestEmail(to, providerConfig);\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.validateEmailProvider();\n    }\n    /**\n   * Limpiar notificaciones antiguas\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.cleanupOldNotifications(daysToKeep);\n    }\n    /**\n   * Obtener resumen del sistema de notificaciones\n   */ static async getSystemSummary() {\n        try {\n            const [providerStatus, recentStats, failureStats, performanceMetrics] = await Promise.all([\n                this.validateEmailProvider(),\n                this.getNotificationStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString()),\n                this.getFailureStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString()),\n                this.getPerformanceMetrics(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString())\n            ]);\n            return {\n                providerStatus,\n                recentStats,\n                failureStats,\n                performanceMetrics\n            };\n        } catch (error) {\n            console.error('Error obteniendo resumen del sistema:', error);\n            throw error;\n        }\n    }\n    /**\n   * Enviar email de confirmación para upgrade de plan\n   * SEGURIDAD: Este email se envía cuando se detecta un pago para un email existente\n   */ static async sendPlanUpgradeConfirmationEmail(userEmail, userName, newPlanId, confirmationToken) {\n        try {\n            console.log(`📧 [SECURITY] Enviando email de confirmación de upgrade a: ${userEmail}`);\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generatePlanUpgradeConfirmation(userName, newPlanId, confirmationToken);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'plan_upgrade_confirmation',\n                metadata: {\n                    newPlanId,\n                    confirmationToken,\n                    userName,\n                    securityAction: true,\n                    sentAt: new Date().toISOString()\n                }\n            };\n            // Enviar email\n            const result = await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n            if (result) {\n                console.log(`✅ [SECURITY] Email de confirmación enviado exitosamente a: ${userEmail}`);\n            } else {\n                console.error(`❌ [SECURITY] Error enviando email de confirmación a: ${userEmail}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('❌ [SECURITY] Error enviando email de confirmación de upgrade:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar alerta de seguridad al administrador\n   */ static async sendSecurityAlert(alertData) {\n        try {\n            console.log(`🚨 [SECURITY] Enviando alerta de seguridad al administrador`);\n            console.log(`🚨 [SECURITY] Discrepancia: ${alertData.payerEmail} → ${alertData.accountOwnerEmail}`);\n            // Lista de emails de administradores (consistente con otros endpoints)\n            const ADMIN_EMAILS = [\n                '<EMAIL>'\n            ];\n            const adminEmail = ADMIN_EMAILS[0]; // Usar el primer administrador\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateSecurityAlertEmail(alertData);\n            // Crear notificación\n            const notification = {\n                to: adminEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'admin_security_alert',\n                metadata: {\n                    alertType: 'payment_email_mismatch',\n                    payerEmail: alertData.payerEmail,\n                    accountOwnerEmail: alertData.accountOwnerEmail,\n                    requestedPlan: alertData.requestedPlan,\n                    paymentAmount: alertData.paymentAmount,\n                    stripeSessionId: alertData.stripeSessionId,\n                    timestamp: alertData.timestamp,\n                    actionTaken: alertData.actionTaken,\n                    securityAlert: true,\n                    sentAt: new Date().toISOString()\n                }\n            };\n            // Enviar email\n            const result = await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n            if (result) {\n                console.log(`✅ [SECURITY] Alerta de seguridad enviada exitosamente al administrador: ${adminEmail}`);\n            } else {\n                console.error(`❌ [SECURITY] Error enviando alerta de seguridad al administrador: ${adminEmail}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('❌ [SECURITY] Error enviando alerta de seguridad:', error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailNotificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailSender.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailSender.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailSender: () => (/* binding */ EmailSender)\n/* harmony export */ });\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\n// src/lib/services/email/emailSender.ts\n// Lógica de envío de emails con reintentos y manejo de errores\n\nclass EmailSender {\n    /**\n   * Función base para enviar emails con reintentos y manejo de errores\n   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)\n   */ static async sendEmail(notification, retryCount = 0) {\n        const maxRetries = 3;\n        const retryDelay = Math.pow(2, retryCount) * 1000; // Backoff exponencial: 1s, 2s, 4s\n        let notificationId = null;\n        try {\n            console.log(`📧 Enviando email (intento ${retryCount + 1}/${maxRetries + 1}):`, {\n                to: notification.to,\n                subject: notification.subject,\n                type: notification.type\n            });\n            // Registrar notificación como 'pending' antes del envío\n            notificationId = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.logEmailNotification(notification, 'pending');\n            // TODO: Implementar con tu proveedor de email real\n            // Ejemplo con Resend:\n            /*\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      const result = await resend.emails.send({\n        from: 'OposI <<EMAIL>>',\n        to: notification.to,\n        subject: notification.subject,\n        html: notification.htmlContent,\n        text: notification.textContent,\n      });\n      \n      if (result.error) {\n        throw new Error(`Resend API error: ${result.error.message}`);\n      }\n      */ // Simulación de envío (remover cuando implementes proveedor real)\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // Simular fallo ocasional para testing (remover en producción)\n            if (Math.random() < 0.1 && retryCount === 0) {\n                throw new Error('Simulated email provider error');\n            }\n            // Actualizar estado a 'sent' si el envío fue exitoso\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'sent');\n            }\n            console.log('✅ Email enviado exitosamente');\n            return true;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`❌ Error enviando email (intento ${retryCount + 1}):`, errorMessage);\n            // Actualizar estado a 'failed' si tenemos el ID\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'failed', errorMessage);\n            }\n            // Intentar reenvío si no hemos alcanzado el máximo de reintentos\n            if (retryCount < maxRetries) {\n                console.log(`🔄 Reintentando envío en ${retryDelay}ms...`);\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                return this.sendEmail(notification, retryCount + 1);\n            }\n            // Si agotamos los reintentos, registrar fallo final\n            console.error(`💥 Fallo definitivo después de ${maxRetries + 1} intentos`);\n            return false;\n        }\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, limit = 10 // Máximo 10 reintentos por ejecución\n    ) {\n        try {\n            console.log(`🔄 Buscando notificaciones fallidas para reintentar (máximo ${maxAge} horas)...`);\n            // Obtener notificaciones fallidas\n            const failedNotifications = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.getFailedNotifications(maxAge, limit);\n            if (failedNotifications.length === 0) {\n                console.log('✅ No se encontraron notificaciones fallidas para reintentar');\n                return {\n                    attempted: 0,\n                    successful: 0,\n                    failed: 0,\n                    errors: []\n                };\n            }\n            console.log(`📋 Encontradas ${failedNotifications.length} notificaciones para reintentar`);\n            let successful = 0;\n            let failed = 0;\n            const errors = [];\n            // Reintentar cada notificación\n            for (const notification of failedNotifications){\n                try {\n                    const emailNotification = {\n                        to: notification.recipient_email,\n                        subject: notification.subject,\n                        htmlContent: '',\n                        textContent: '',\n                        type: notification.type,\n                        userId: notification.user_id,\n                        metadata: notification.metadata\n                    };\n                    // Marcar como reintento en metadata\n                    emailNotification.metadata = {\n                        ...emailNotification.metadata,\n                        retry_attempt: true,\n                        original_notification_id: notification.id,\n                        retry_at: new Date().toISOString()\n                    };\n                    const success = await this.sendEmail(emailNotification);\n                    if (success) {\n                        successful++;\n                        // Marcar la notificación original como reintentada exitosamente\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, true);\n                    } else {\n                        failed++;\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, 'Retry failed');\n                        errors.push(`Failed to retry notification ${notification.id}`);\n                    }\n                } catch (retryError) {\n                    failed++;\n                    const errorMsg = `Error retrying notification ${notification.id}: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                    await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, errorMsg);\n                }\n            }\n            console.log(`🎯 Reintentos completados: ${successful} exitosos, ${failed} fallidos`);\n            return {\n                attempted: failedNotifications.length,\n                successful,\n                failed,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en retryFailedNotifications:', error);\n            throw error;\n        }\n    }\n    /**\n   * Enviar email de prueba para verificar configuración\n   */ static async sendTestEmail(to, providerConfig) {\n        try {\n            const testNotification = {\n                to,\n                subject: 'Test Email - OposI',\n                htmlContent: `\n          <h1>Email de Prueba</h1>\n          <p>Este es un email de prueba para verificar la configuración del sistema de notificaciones.</p>\n          <p>Enviado el: ${new Date().toLocaleString('es-ES')}</p>\n        `,\n                textContent: `\n          Email de Prueba\n          \n          Este es un email de prueba para verificar la configuración del sistema de notificaciones.\n          Enviado el: ${new Date().toLocaleString('es-ES')}\n        `,\n                type: 'other',\n                metadata: {\n                    test_email: true,\n                    sent_at: new Date().toISOString()\n                }\n            };\n            const success = await this.sendEmail(testNotification);\n            return {\n                success,\n                message: success ? 'Email de prueba enviado exitosamente' : 'Fallo al enviar email de prueba',\n                details: {\n                    to,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        } catch (error) {\n            console.error('Error enviando email de prueba:', error);\n            return {\n                success: false,\n                message: 'Error enviando email de prueba',\n                details: {\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                }\n            };\n        }\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        try {\n            // TODO: Implementar validación específica del proveedor\n            // Ejemplo para Resend:\n            /*\n      if (!process.env.RESEND_API_KEY) {\n        return {\n          isValid: false,\n          provider: 'Resend',\n          message: 'RESEND_API_KEY no configurada'\n        };\n      }\n      \n      // Probar conexión con API\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      await resend.domains.list();\n      */ // Por ahora, simulación\n            return {\n                isValid: true,\n                provider: 'Simulado',\n                message: 'Proveedor de email configurado correctamente'\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                provider: 'Unknown',\n                message: error instanceof Error ? error.message : 'Error validando proveedor'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailSender.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailTemplates.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailTemplates.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// src/lib/services/email/emailTemplates.ts\n// Templates para diferentes tipos de notificaciones por email\nclass EmailTemplates {\n    /**\n   * Template para notificación de cancelación de suscripción\n   */ static generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        const daysRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Suscripción Cancelada - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">Suscripción Cancelada</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>\n          \n          <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #059669;\">📅 Período de Gracia Activo</h3>\n            <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>\n            <p><strong>Días restantes:</strong> ${daysRemaining} días</p>\n            <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>\n          </div>\n          \n          <h3>¿Qué sucede después?</h3>\n          <ul>\n            <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>\n            <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>\n            <li>Conservarás acceso a las funciones básicas de OposI</li>\n            <li>Tus documentos y progreso se mantendrán guardados</li>\n          </ul>\n          \n          <h3>¿Cambiaste de opinión?</h3>\n          <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>\n          <p style=\"text-align: center; margin: 20px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              Reactivar Suscripción\n            </a>\n          </p>\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nSuscripción Cancelada - OposI\n\nHola ${userName},\n\nHemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.\n\nPERÍODO DE GRACIA ACTIVO:\n- Mantienes acceso completo hasta: ${formattedDate}\n- Días restantes: ${daysRemaining} días\n\n¿Qué sucede después?\n- Tu acceso a las funciones premium finalizará el ${formattedDate}\n- Tu cuenta se convertirá automáticamente al Plan Gratuito\n- Conservarás acceso a las funciones básicas de OposI\n- Tus documentos y progreso se mantendrán guardados\n\n¿Cambiaste de opinión?\nPuedes reactivar tu suscripción en cualquier momento desde: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        const subject = `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template para recordatorio de que el período de gracia está por terminar\n   */ static generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n        const hoursRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Tu acceso premium termina pronto - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #dc2626;\">⏰ Tu acceso premium termina pronto</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>\n          \n          <div style=\"background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;\">\n            <h3 style=\"margin-top: 0; color: #92400e;\">¿Quieres continuar con tu plan premium?</h3>\n            <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>\n          </div>\n          \n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;\">\n              Reactivar Mi Suscripción\n            </a>\n          </p>\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nTu acceso premium termina pronto - OposI\n\nHola ${userName},\n\nTe recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).\n\n¿Quieres continuar con tu plan premium?\nReactivar tu suscripción: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n\nEquipo de OposI\n    `;\n        const subject = `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template base para otros tipos de notificaciones\n   */ static generateGenericEmail(userName, title, message, ctaText, ctaUrl) {\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>${title} - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">${title}</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>${message}</p>\n          \n          ${ctaText && ctaUrl ? `\n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${ctaUrl}\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              ${ctaText}\n            </a>\n          </p>\n          ` : ''}\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n${title} - OposI\n\nHola ${userName},\n\n${message}\n\n${ctaText && ctaUrl ? `${ctaText}: ${ctaUrl}` : ''}\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject: title\n        };\n    }\n    /**\n   * Template para confirmación de upgrade de plan (SEGURIDAD)\n   */ static generatePlanUpgradeConfirmation(userName, newPlanId, confirmationToken) {\n        const confirmationUrl = `${\"http://localhost:3000\"}/auth/confirm-upgrade?token=${confirmationToken}`;\n        const planDisplayName = newPlanId === 'usuario' ? 'Usuario' : newPlanId === 'pro' ? 'Pro' : newPlanId;\n        const subject = `⚠️ Acción Requerida: Confirma la actualización de tu plan en OposiAI`;\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Confirma la Actualización de tu Plan - OposiAI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: #2563eb; margin-bottom: 10px;\">🔒 Confirma la Actualización de tu Plan</h1>\n          </div>\n\n          <div style=\"background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 4px;\">\n            <h3 style=\"margin-top: 0; color: #92400e;\">⚠️ Acción de Seguridad Requerida</h3>\n            <p style=\"margin-bottom: 0;\"><strong>Hemos detectado un intento de actualización de plan en tu cuenta.</strong></p>\n          </div>\n\n          <p>Hola <strong>${userName}</strong>,</p>\n\n          <p>Hemos recibido una solicitud para actualizar tu cuenta al <strong>Plan ${planDisplayName}</strong>. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.</p>\n\n          <p><strong>Para proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.</strong></p>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${confirmationUrl}\"\n               style=\"background-color: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;\">\n              ✅ Sí, actualizar mi plan\n            </a>\n          </div>\n\n          <div style=\"background-color: #fee2e2; border-left: 4px solid #dc2626; padding: 20px; margin: 20px 0; border-radius: 4px;\">\n            <h4 style=\"margin-top: 0; color: #991b1b;\">🚨 Si NO has solicitado esta actualización:</h4>\n            <ul style=\"margin-bottom: 0;\">\n              <li>Por favor, <strong>ignora este email</strong></li>\n              <li>Contacta inmediatamente con nuestro soporte</li>\n              <li>Considera cambiar tu contraseña por seguridad</li>\n            </ul>\n          </div>\n\n          <div style=\"background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n            <p style=\"margin: 0; font-size: 14px; color: #6b7280;\">\n              <strong>Información de seguridad:</strong><br>\n              • Este enlace es válido durante 24 horas<br>\n              • Solo tú puedes confirmar esta actualización<br>\n              • El pago se procesará únicamente tras tu confirmación\n            </p>\n          </div>\n\n          <hr style=\"border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;\">\n\n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.<br>\n            <strong>Equipo de OposiAI</strong>\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n⚠️ ACCIÓN REQUERIDA: Confirma la actualización de tu plan en OposiAI\n\nHola ${userName},\n\n🔒 ACCIÓN DE SEGURIDAD REQUERIDA\n\nHemos recibido una solicitud para actualizar tu cuenta al Plan ${planDisplayName}. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.\n\nPara proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.\n\nCONFIRMAR ACTUALIZACIÓN:\n${confirmationUrl}\n\n🚨 SI NO HAS SOLICITADO ESTA ACTUALIZACIÓN:\n- Por favor, ignora este email\n- Contacta inmediatamente con nuestro soporte\n- Considera cambiar tu contraseña por seguridad\n\nINFORMACIÓN DE SEGURIDAD:\n• Este enlace es válido durante 24 horas\n• Solo tú puedes confirmar esta actualización\n• El pago se procesará únicamente tras tu confirmación\n\nSi tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.\n\nEquipo de OposiAI\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template para alerta de seguridad al administrador\n   */ static generateSecurityAlertEmail(alertData) {\n        const formattedAmount = (alertData.paymentAmount / 100).toFixed(2);\n        const formattedTimestamp = new Date(alertData.timestamp).toLocaleString('es-ES');\n        const planDisplayName = alertData.requestedPlan === 'usuario' ? 'Usuario' : alertData.requestedPlan === 'pro' ? 'Pro' : alertData.requestedPlan;\n        const subject = `🚨 ALERTA DE SEGURIDAD: Discrepancia en pago - ${alertData.payerEmail}`;\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Alerta de Seguridad - OposiAI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n        <div style=\"background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"margin: 0; font-size: 28px; font-weight: bold;\">🚨 ALERTA DE SEGURIDAD</h1>\n          <p style=\"margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;\">Discrepancia detectada en pago</p>\n        </div>\n\n        <div style=\"background: #fef2f2; border: 2px solid #fecaca; border-radius: 8px; padding: 20px; margin-bottom: 25px;\">\n          <h2 style=\"color: #dc2626; margin-top: 0;\">⚠️ Discrepancia Detectada</h2>\n          <p style=\"margin-bottom: 15px;\">Se ha detectado un pago realizado por un email diferente al propietario de la cuenta:</p>\n\n          <div style=\"background: white; padding: 15px; border-radius: 6px; margin: 15px 0;\">\n            <p style=\"margin: 5px 0;\"><strong>👤 Email del pagador:</strong> ${alertData.payerEmail}</p>\n            <p style=\"margin: 5px 0;\"><strong>🏠 Propietario de la cuenta:</strong> ${alertData.accountOwnerEmail}</p>\n          </div>\n        </div>\n\n        <div style=\"background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 25px;\">\n          <h3 style=\"margin-top: 0; color: #374151;\">📊 Detalles del Pago</h3>\n          <ul style=\"list-style: none; padding: 0;\">\n            <li style=\"margin: 8px 0;\"><strong>💳 Plan solicitado:</strong> ${planDisplayName}</li>\n            <li style=\"margin: 8px 0;\"><strong>💰 Monto:</strong> ${formattedAmount}€</li>\n            <li style=\"margin: 8px 0;\"><strong>🏦 Moneda:</strong> ${alertData.currency.toUpperCase()}</li>\n            <li style=\"margin: 8px 0;\"><strong>🔗 Sesión Stripe:</strong> <code style=\"background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-size: 12px;\">${alertData.stripeSessionId}</code></li>\n            <li style=\"margin: 8px 0;\"><strong>⏰ Timestamp:</strong> ${formattedTimestamp}</li>\n          </ul>\n        </div>\n\n        <div style=\"background: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 8px; padding: 20px; margin-bottom: 25px;\">\n          <h3 style=\"margin-top: 0; color: #065f46;\">✅ Acción Tomada</h3>\n          <p style=\"margin-bottom: 10px;\">${alertData.actionTaken}</p>\n          <p style=\"font-size: 14px; color: #047857;\">El sistema ha activado automáticamente el protocolo de seguridad para proteger la cuenta del usuario.</p>\n        </div>\n\n        <div style=\"background: #fffbeb; border: 1px solid #fde68a; border-radius: 8px; padding: 20px; margin-bottom: 25px;\">\n          <h3 style=\"margin-top: 0; color: #92400e;\">🔍 Acciones Recomendadas</h3>\n          <ol style=\"margin: 0; padding-left: 20px;\">\n            <li style=\"margin: 8px 0;\">Verificar si el pago es legítimo contactando al usuario</li>\n            <li style=\"margin: 8px 0;\">Revisar el historial de la cuenta para patrones sospechosos</li>\n            <li style=\"margin: 8px 0;\">Monitorear si el usuario confirma o rechaza la actualización</li>\n            <li style=\"margin: 8px 0;\">Considerar contactar al pagador si es necesario</li>\n          </ol>\n        </div>\n\n        <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n          <p style=\"font-size: 14px; color: #6b7280; margin: 0;\">\n            Esta alerta se generó automáticamente por el sistema de seguridad de OposiAI\n          </p>\n          <p style=\"font-size: 12px; color: #9ca3af; margin: 5px 0 0 0;\">\n            ${formattedTimestamp}\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n🚨 ALERTA DE SEGURIDAD - OposiAI\n\nDISCREPANCIA DETECTADA EN PAGO\n\nSe ha detectado un pago realizado por un email diferente al propietario de la cuenta:\n\n👤 Email del pagador: ${alertData.payerEmail}\n🏠 Propietario de la cuenta: ${alertData.accountOwnerEmail}\n\nDETALLES DEL PAGO:\n💳 Plan solicitado: ${planDisplayName}\n💰 Monto: ${formattedAmount}€\n🏦 Moneda: ${alertData.currency.toUpperCase()}\n🔗 Sesión Stripe: ${alertData.stripeSessionId}\n⏰ Timestamp: ${formattedTimestamp}\n\nACCIÓN TOMADA:\n${alertData.actionTaken}\n\nEl sistema ha activado automáticamente el protocolo de seguridad para proteger la cuenta del usuario.\n\nACCIONES RECOMENDADAS:\n1. Verificar si el pago es legítimo contactando al usuario\n2. Revisar el historial de la cuenta para patrones sospechosos\n3. Monitorear si el usuario confirma o rechaza la actualización\n4. Considerar contactar al pagador si es necesario\n\nEsta alerta se generó automáticamente por el sistema de seguridad de OposiAI\n${formattedTimestamp}\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fnotifications%2Froute&page=%2Fapi%2Fuser%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();